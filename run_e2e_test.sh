#!/bin/bash

# 浏览器扩展到Tauri主应用E2E通信测试执行脚本

# 注释掉 set -e 以便测试失败时脚本继续执行
# set -e

echo "🚀 开始执行浏览器扩展到Tauri主应用的完整E2E通信测试"
echo "===================================================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    ((PASSED_TESTS++))
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    ((FAILED_TESTS++))
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 测试步骤函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    ((TOTAL_TESTS++))
    log_info "执行测试: $test_name"
    
    if eval "$test_command"; then
        log_success "$test_name - 通过"
        return 0
    else
        log_error "$test_name - 失败"
        return 1
    fi
}

# 1. 环境准备检查
echo ""
log_info "第1步: 环境准备检查"
echo "--------------------------------------------------------------------"

# 检查Rust工具链
run_test "Rust工具链检查" "rustc --version && cargo --version"

# 检查必要的依赖
run_test "系统依赖检查" "which node && which npm"

# 2. 构建所有组件
echo ""
log_info "第2步: 构建所有组件"
echo "--------------------------------------------------------------------"

# 构建daemon
run_test "构建Daemon" "cd src-tauri/daemon && cargo build"

# 构建主应用
run_test "构建Tauri主应用" "cd src-tauri && cargo build"

# 构建浏览器扩展
run_test "构建浏览器扩展" "cd secure-password-ext && npm install && npm run build"

# 3. 启动组件测试
echo ""
log_info "第3步: 组件启动测试"
echo "--------------------------------------------------------------------"

# 测试Daemon启动
test_daemon_startup() {
    log_info "测试Daemon启动..."
    
    # 启动daemon（后台模式）
    cd src-tauri/daemon
    timeout 10s cargo run -- --test-mode &
    DAEMON_PID=$!
    
    # 等待启动
    sleep 3
    
    # 检查进程是否存在
    if ps -p $DAEMON_PID > /dev/null; then
        log_success "Daemon启动成功 (PID: $DAEMON_PID)"
        kill $DAEMON_PID 2>/dev/null || true
        return 0
    else
        log_error "Daemon启动失败"
        return 1
    fi
}

run_test "Daemon启动测试" "test_daemon_startup"

# 4. Native Messaging配置测试
echo ""
log_info "第4步: Native Messaging配置测试"
echo "--------------------------------------------------------------------"

# 检查配置文件
test_native_messaging_config() {
    local config_file="secure-password-ext/public/com.secure_password.native_messaging_host_macos.json"
    
    if [[ -f "$config_file" ]]; then
        log_info "检查Native Messaging配置文件..."
        
        # 验证JSON格式
        if jq . "$config_file" > /dev/null 2>&1; then
            log_success "配置文件格式正确"
            return 0
        else
            log_error "配置文件JSON格式错误"
            return 1
        fi
    else
        log_error "配置文件不存在: $config_file"
        return 1
    fi
}

run_test "Native Messaging配置检查" "test_native_messaging_config"

# 5. 通信协议测试
echo ""
log_info "第5步: 通信协议测试"
echo "--------------------------------------------------------------------"

# 测试协议编解码
test_protocol_codec() {
    log_info "测试协议编解码..."
    
    cd src-tauri/daemon
    cargo test test_native_messaging_basic_integration --lib -- --nocapture
}

run_test "协议编解码测试" "test_protocol_codec"

# 6. 集成测试
echo ""
log_info "第6步: 运行集成测试"
echo "--------------------------------------------------------------------"

# 运行daemon的集成测试
test_daemon_integration() {
    log_info "运行Daemon集成测试..."
    
    cd src-tauri/daemon
    cargo test native_messaging_final_integration --release -- --nocapture
}

run_test "Daemon集成测试" "test_daemon_integration"

# 7. 浏览器扩展测试
echo ""
log_info "第7步: 浏览器扩展测试"
echo "--------------------------------------------------------------------"

# 测试扩展构建
test_extension_build() {
    log_info "测试浏览器扩展构建..."
    
    cd secure-password-ext
    
    # 检查构建产物
    if [[ -d "dist" ]] && [[ -f "dist/manifest.json" ]]; then
        log_success "浏览器扩展构建产物存在"
        return 0
    else
        log_error "浏览器扩展构建产物缺失"
        return 1
    fi
}

run_test "浏览器扩展构建检查" "test_extension_build"

# 8. E2E流程模拟测试
echo ""
log_info "第8步: E2E流程模拟测试"
echo "--------------------------------------------------------------------"

# 模拟完整E2E流程
test_e2e_simulation() {
    log_info "执行E2E流程模拟..."
    
    # 编译并运行E2E测试
    rustc e2e_browser_tauri_test.rs --extern tokio --extern serde_json --extern futures -o e2e_test || return 1
    
    # 运行E2E测试
    timeout 60s ./e2e_test
}

run_test "E2E流程模拟测试" "test_e2e_simulation"

# 9. 性能基准测试
echo ""
log_info "第9步: 性能基准测试"
echo "--------------------------------------------------------------------"

# 性能测试
test_performance() {
    log_info "执行性能基准测试..."
    
    cd src-tauri/daemon
    cargo test test_performance_benchmark --release -- --nocapture
}

run_test "性能基准测试" "test_performance"

# 10. 清理工作
echo ""
log_info "第10步: 清理测试环境"
echo "--------------------------------------------------------------------"

cleanup_test_environment() {
    log_info "清理测试环境..."
    
    # 清理临时文件
    rm -f e2e_test
    
    # 停止可能残留的进程
    pkill -f "secure-password-daemon" 2>/dev/null || true
    pkill -f "secure-password" 2>/dev/null || true
    
    log_success "测试环境清理完成"
}

run_test "环境清理" "cleanup_test_environment"

# 测试结果汇总
echo ""
echo "===================================================================="
echo "🏁 浏览器扩展到Tauri主应用E2E通信测试 - 结果汇总"
echo "===================================================================="

echo ""
echo "📊 测试统计:"
echo "  总测试数: $TOTAL_TESTS"
echo "  通过数量: $PASSED_TESTS"
echo "  失败数量: $FAILED_TESTS"

if [[ $FAILED_TESTS -eq 0 ]]; then
    echo ""
    log_success "🎉 所有E2E通信测试通过！"
    echo ""
    echo "✅ 浏览器扩展到Tauri主应用的完整通信链路验证成功："
    echo "   • 浏览器扩展 → Native Messaging Host ✅"
    echo "   • Native Messaging Host → Daemon IPC ✅"  
    echo "   • Daemon IPC → Tauri主应用 ✅"
    echo "   • 端到端密码管理流程 ✅"
    echo "   • 错误处理和恢复机制 ✅"
    echo "   • 性能基准达标 ✅"
    echo ""
    echo "🚀 系统已准备好进行生产部署！"
    exit 0
else
    echo ""
    log_error "❌ 存在 $FAILED_TESTS 个测试失败"
    echo ""
    echo "⚠️ 请检查以下方面："
    echo "   • 组件构建是否成功"
    echo "   • 依赖库是否正确安装"
    echo "   • 配置文件是否正确"
    echo "   • 系统权限是否足够"
    echo "   • 网络连接是否正常"
    echo ""
    echo "🔧 建议查看详细错误日志并逐个修复问题。"
    exit 1
fi 