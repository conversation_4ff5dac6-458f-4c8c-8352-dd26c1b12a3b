//! IPC 模块集成测试

#[cfg(test)]
mod tests {
    use super::super::*;
    use crate::ipc::{
        IpcMessage, IpcResponse, IpcError,
        TauriIpcReceiver, DaemonIpcClient, MessageRouter,
        CredentialsHandler, SettingsHandler, AuthHandler, HealthHandler
    };
    use crate::ipc::router::MessageMiddleware;
    use tokio::time::Duration;

    /// 创建测试用的 IPC 消息
    fn create_test_message(message_type: &str, payload: serde_json::Value) -> IpcMessage {
        IpcMessage::new_with_response(message_type.to_string(), payload)
    }

    /// 创建测试用的应用句柄（模拟）
    fn create_mock_app_handle() -> tauri::AppHandle {
        // 注意：这需要在实际的 Tauri 应用上下文中运行
        // 在单元测试中，我们可能需要使用模拟对象
        panic!("需要在集成测试环境中运行")
    }

    #[tokio::test]
    async fn test_ipc_message_creation() {
        let payload = serde_json::json!({"test": "data"});
        let message = create_test_message("test_type", payload.clone());
        
        assert!(!message.message_id.is_empty());
        assert_eq!(message.message_type, "test_type");
        assert_eq!(message.payload, payload);
        assert!(message.response_required);
        assert!(message.validate().is_ok());
    }

    #[tokio::test]
    async fn test_ipc_response_creation() {
        let payload = serde_json::json!({"result": "success"});
        let response = IpcResponse::success("test_id".to_string(), payload.clone());
        
        assert_eq!(response.request_id, "test_id");
        assert_eq!(response.status, crate::ipc::protocol::ResponseStatus::Success);
        assert_eq!(response.payload, payload);
        assert!(response.error.is_none());
    }

    #[tokio::test]
    async fn test_error_response_creation() {
        let response = IpcResponse::error("test_id", "test error");
        
        assert_eq!(response.request_id, "test_id");
        assert_eq!(response.status, crate::ipc::protocol::ResponseStatus::Error);
        assert_eq!(response.error, Some("test error".to_string()));
    }

    #[tokio::test]
    async fn test_message_router_creation() {
        let router = MessageRouter::new();
        
        // 测试默认处理器是否注册
        assert!(router.supports_message_type("get_credentials"));
        assert!(router.supports_message_type("save_credential"));
        assert!(router.supports_message_type("get_settings"));
        assert!(router.supports_message_type("auth_request"));
        assert!(router.supports_message_type("health_check"));
        
        // 测试不支持的消息类型
        assert!(!router.supports_message_type("unknown_type"));
    }

    #[tokio::test]
    async fn test_credentials_handler() {
        let handler = CredentialsHandler::new();
        
        assert_eq!(handler.name(), "CredentialsHandler");
        assert!(handler.can_handle("get_credentials"));
        assert!(handler.can_handle("save_credential"));
        assert!(handler.can_handle("update_credential"));
        assert!(handler.can_handle("delete_credential"));
        assert!(handler.can_handle("search_credentials"));
        assert!(!handler.can_handle("unknown_type"));
    }

    #[tokio::test]
    async fn test_settings_handler() {
        let handler = SettingsHandler::new();
        
        assert_eq!(handler.name(), "SettingsHandler");
        assert!(handler.can_handle("get_settings"));
        assert!(handler.can_handle("update_settings"));
        assert!(handler.can_handle("reset_settings"));
        assert!(!handler.can_handle("unknown_type"));
    }

    #[tokio::test]
    async fn test_auth_handler() {
        let handler = AuthHandler::new();
        
        assert_eq!(handler.name(), "AuthHandler");
        assert!(handler.can_handle("auth_request"));
        assert!(handler.can_handle("auth_challenge"));
        assert!(!handler.can_handle("unknown_type"));
    }

    #[tokio::test]
    async fn test_health_handler() {
        let handler = HealthHandler::new();
        
        assert_eq!(handler.name(), "HealthHandler");
        assert!(handler.can_handle("health_check"));
        assert!(handler.can_handle("ping"));
        assert!(!handler.can_handle("unknown_type"));
    }

    #[tokio::test]
    async fn test_ipc_error_properties() {
        let connection_error = IpcError::ConnectionError("test".to_string());
        assert!(connection_error.is_retryable());
        assert!(!connection_error.is_fatal());
        assert_eq!(connection_error.category(), crate::ipc::error::ErrorCategory::Network);
        
        let config_error = IpcError::ConfigError("test".to_string());
        assert!(!config_error.is_retryable());
        assert!(config_error.is_fatal());
        assert_eq!(config_error.category(), crate::ipc::error::ErrorCategory::Configuration);
        
        let auth_error = IpcError::AuthenticationError("test".to_string());
        assert!(!auth_error.is_retryable());
        assert!(auth_error.is_fatal());
        assert_eq!(auth_error.category(), crate::ipc::error::ErrorCategory::Security);
    }

    #[tokio::test]
    async fn test_message_priority_ordering() {
        use crate::ipc::protocol::MessagePriority;
        
        assert!(MessagePriority::Low < MessagePriority::Normal);
        assert!(MessagePriority::Normal < MessagePriority::High);
        assert!(MessagePriority::High < MessagePriority::Critical);
    }

    #[tokio::test]
    async fn test_message_type_conversion() {
        use crate::ipc::protocol::IpcMessageType;
        
        let msg_type = IpcMessageType::GetCredentials;
        assert_eq!(msg_type.as_str(), "get_credentials");
        assert_eq!(IpcMessageType::from_str("get_credentials"), msg_type);
        
        let custom_type = IpcMessageType::Custom("custom_message".to_string());
        assert_eq!(custom_type.as_str(), "custom_message");
    }

    #[tokio::test]
    async fn test_message_type_auth_requirements() {
        use crate::ipc::protocol::IpcMessageType;
        
        // 需要认证的消息类型
        assert!(IpcMessageType::GetCredentials.requires_auth());
        assert!(IpcMessageType::SaveCredential.requires_auth());
        assert!(IpcMessageType::UpdateCredential.requires_auth());
        assert!(IpcMessageType::DeleteCredential.requires_auth());
        assert!(IpcMessageType::SearchCredentials.requires_auth());
        assert!(IpcMessageType::GetSettings.requires_auth());
        assert!(IpcMessageType::UpdateSettings.requires_auth());
        
        // 不需要认证的消息类型
        assert!(!IpcMessageType::HealthCheck.requires_auth());
        assert!(!IpcMessageType::Ping.requires_auth());
        assert!(!IpcMessageType::AuthRequest.requires_auth());
    }

    #[tokio::test]
    async fn test_message_timeout_values() {
        use crate::ipc::protocol::IpcMessageType;
        
        assert_eq!(IpcMessageType::HealthCheck.default_timeout_ms(), 5000);
        assert_eq!(IpcMessageType::Ping.default_timeout_ms(), 5000);
        assert_eq!(IpcMessageType::GetCredentials.default_timeout_ms(), 10000);
        assert_eq!(IpcMessageType::SaveCredential.default_timeout_ms(), 15000);
        assert_eq!(IpcMessageType::SyncRequest.default_timeout_ms(), 30000);
    }

    #[tokio::test]
    async fn test_config_validation() {
        let mut config = IpcReceiverConfig::default();
        assert!(config.validate().is_ok());
        
        // 测试无效配置
        config.daemon_address = String::new();
        assert!(config.validate().is_err());
        
        config = IpcReceiverConfig::default();
        config.max_reconnect_attempts = 0;
        assert!(config.validate().is_err());
        
        config = IpcReceiverConfig::default();
        config.max_concurrent_messages = 0;
        assert!(config.validate().is_err());
        
        config = IpcReceiverConfig::default();
        config.enable_message_queue = true;
        config.queue_size = 0;
        assert!(config.validate().is_err());
    }

    #[tokio::test]
    async fn test_message_expiration() {
        let message = create_test_message("test", serde_json::json!({}));
        
        // 消息应该没有过期（刚创建）
        assert!(!message.is_expired(10000)); // 10秒超时
        
        // 测试过期检查（使用很短的超时时间）
        tokio::time::sleep(Duration::from_millis(10)).await;
        assert!(message.is_expired(5)); // 5毫秒超时，应该已过期
    }

    #[tokio::test]
    async fn test_daemon_connection_config() {
        let config = DaemonConnectionConfig::default();
        
        assert!(!config.address.is_empty());
        assert!(config.timeout.as_secs() > 0);
        assert!(config.max_reconnect_attempts > 0);
        assert!(config.reconnect_delay.as_secs() > 0);
        assert!(config.message_timeout.as_secs() > 0);
        assert!(config.enable_heartbeat);
        assert!(config.heartbeat_interval.as_secs() > 0);
    }

    // 注意：以下测试需要实际的网络连接和守护进程，通常在集成测试中运行

    #[tokio::test]
    #[ignore] // 需要实际的守护进程运行
    async fn test_daemon_client_connection() {
        // 这个测试需要实际的守护进程在运行
        let result = DaemonIpcClient::connect("127.0.0.1:8080").await;
        
        match result {
            Ok(client) => {
                assert!(client.is_connected().await);
                
                // 测试健康检查
                let health_result = client.send_heartbeat().await;
                assert!(health_result.is_ok());
                
                // 关闭连接
                assert!(client.close().await.is_ok());
            }
            Err(e) => {
                // 如果守护进程未运行，这是预期的
                println!("守护进程连接失败（预期）: {}", e);
            }
        }
    }

    #[tokio::test]
    #[ignore] // 需要实际的 Tauri 应用上下文
    async fn test_ipc_receiver_initialization() {
        // 这个测试需要在实际的 Tauri 应用上下文中运行
        let app_handle = create_mock_app_handle();
        
        let result = TauriIpcReceiver::initialize(app_handle).await;
        
        match result {
            Ok(receiver) => {
                assert!(!receiver.is_running().await);
                assert!(!receiver.get_supported_message_types().is_empty());
                
                let config = receiver.get_config();
                assert!(!config.daemon_address.is_empty());
                assert!(config.max_reconnect_attempts > 0);
            }
            Err(e) => {
                println!("IPC 接收端初始化失败（可能是预期的）: {}", e);
            }
        }
    }

    #[tokio::test]
    async fn test_middleware_functionality() {
        use crate::ipc::router::{LoggingMiddleware, AuthenticationMiddleware, RateLimitMiddleware};
        
        // 测试日志中间件
        let logging_middleware = LoggingMiddleware::new();
        assert_eq!(logging_middleware.name(), "LoggingMiddleware");
        
        // 测试认证中间件
        let auth_middleware = AuthenticationMiddleware::new();
        assert_eq!(auth_middleware.name(), "AuthenticationMiddleware");
        
        // 测试限流中间件
        let rate_limit_middleware = RateLimitMiddleware::new(10);
        assert_eq!(rate_limit_middleware.name(), "RateLimitMiddleware");
    }

    #[tokio::test]
    async fn test_rate_limiting() {
        use crate::ipc::router::RateLimitMiddleware;
        
        let middleware = RateLimitMiddleware::new(2);
        
        // 前两次请求应该成功
        assert!(middleware.check_rate_limit("test_source").is_ok());
        assert!(middleware.check_rate_limit("test_source").is_ok());
        
        // 第三次请求应该失败
        assert!(middleware.check_rate_limit("test_source").is_err());
        
        // 不同来源应该有独立的限制
        assert!(middleware.check_rate_limit("other_source").is_ok());
    }

    #[tokio::test]
    async fn test_error_severity_levels() {
        use crate::ipc::error::{IpcError, ErrorSeverity};
        
        let critical_error = IpcError::ConfigError("test".to_string());
        assert_eq!(critical_error.severity(), ErrorSeverity::Critical);
        
        let high_error = IpcError::ProtocolError("test".to_string());
        assert_eq!(high_error.severity(), ErrorSeverity::High);
        
        let medium_error = IpcError::ConnectionError("test".to_string());
        assert_eq!(medium_error.severity(), ErrorSeverity::Medium);
        
        let low_error = IpcError::SerializationError("test".to_string());
        assert_eq!(low_error.severity(), ErrorSeverity::Low);
    }
}

/// 性能测试模块
#[cfg(test)]
mod performance_tests {
    use super::super::*;
    use std::time::Instant;
    use tokio::time::Duration;

    #[tokio::test]
    async fn test_message_serialization_performance() {
        let message = IpcMessage::new(
            "test_type".to_string(),
            serde_json::json!({
                "large_data": vec![0u8; 1024], // 1KB 数据
                "timestamp": chrono::Utc::now().timestamp(),
                "metadata": {
                    "source": "performance_test",
                    "version": "1.0.0"
                }
            })
        );
        
        let start = Instant::now();
        
        // 执行1000次序列化/反序列化
        for _ in 0..1000 {
            let serialized = serde_json::to_vec(&message).unwrap();
            let _deserialized: IpcMessage = serde_json::from_slice(&serialized).unwrap();
        }
        
        let duration = start.elapsed();
        println!("1000次消息序列化/反序列化耗时: {:?}", duration);
        
        // 性能要求：1000次操作应在100ms内完成
        assert!(duration < Duration::from_millis(100));
    }

    #[tokio::test]
    async fn test_message_router_performance() {
        let router = MessageRouter::new();
        let start = Instant::now();
        
        // 测试1000次消息类型检查
        for i in 0..1000 {
            let message_type = if i % 4 == 0 {
                "get_credentials"
            } else if i % 4 == 1 {
                "save_credential"
            } else if i % 4 == 2 {
                "get_settings"
            } else {
                "health_check"
            };
            
            assert!(router.supports_message_type(message_type));
        }
        
        let duration = start.elapsed();
        println!("1000次消息路由检查耗时: {:?}", duration);
        
        // 性能要求：1000次路由检查应在10ms内完成
        assert!(duration < Duration::from_millis(10));
    }
}
