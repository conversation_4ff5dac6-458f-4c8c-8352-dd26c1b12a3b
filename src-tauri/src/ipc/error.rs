//! IPC 错误处理模块

use thiserror::Error;

/// IPC 错误类型
#[derive(Error, Debug)]
pub enum IpcError {
    #[error("连接错误: {0}")]
    ConnectionError(String),
    
    #[error("连接不可用")]
    ConnectionNotAvailable,
    
    #[error("消息序列化错误: {0}")]
    SerializationError(String),
    
    #[error("消息反序列化错误: {0}")]
    DeserializationError(String),
    
    #[error("网络错误: {0}")]
    NetworkError(String),
    
    #[error("超时错误: {0}")]
    TimeoutError(String),
    
    #[error("配置错误: {0}")]
    ConfigError(String),
    
    #[error("认证错误: {0}")]
    AuthenticationError(String),
    
    #[error("权限错误: {0}")]
    PermissionError(String),
    
    #[error("消息处理错误: {0}")]
    MessageHandlingError(String),
    
    #[error("路由错误: {0}")]
    RoutingError(String),
    
    #[error("协议错误: {0}")]
    ProtocolError(String),
    
    #[error("加密错误: {0}")]
    EncryptionError(String),
    
    #[error("解密错误: {0}")]
    DecryptionError(String),
    
    #[error("签名验证错误: {0}")]
    SignatureVerificationError(String),
    
    #[error("重连失败: 已达到最大重连尝试次数")]
    MaxReconnectAttemptsExceeded,
    
    #[error("消息队列已满")]
    MessageQueueFull,
    
    #[error("不支持的消息类型: {0}")]
    UnsupportedMessageType(String),
    
    #[error("无效的消息格式: {0}")]
    InvalidMessageFormat(String),
    
    #[error("守护进程未响应")]
    DaemonNotResponding,
    
    #[error("健康检查失败: {0}")]
    HealthCheckFailed(String),
    
    #[error("内部错误: {0}")]
    InternalError(String),
    
    #[error("IO 错误: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("JSON 错误: {0}")]
    JsonError(#[from] serde_json::Error),
    
    #[error("Tokio 任务错误: {0}")]
    TokioJoinError(#[from] tokio::task::JoinError),
}

/// IPC 结果类型
pub type IpcResult<T> = Result<T, IpcError>;

impl IpcError {
    /// 检查错误是否可重试
    pub fn is_retryable(&self) -> bool {
        match self {
            IpcError::ConnectionError(_) => true,
            IpcError::NetworkError(_) => true,
            IpcError::TimeoutError(_) => true,
            IpcError::DaemonNotResponding => true,
            IpcError::HealthCheckFailed(_) => true,
            IpcError::IoError(_) => true,
            _ => false,
        }
    }
    
    /// 检查错误是否为致命错误
    pub fn is_fatal(&self) -> bool {
        match self {
            IpcError::ConfigError(_) => true,
            IpcError::AuthenticationError(_) => true,
            IpcError::PermissionError(_) => true,
            IpcError::ProtocolError(_) => true,
            IpcError::MaxReconnectAttemptsExceeded => true,
            IpcError::UnsupportedMessageType(_) => true,
            _ => false,
        }
    }
    
    /// 获取错误的严重程度
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            IpcError::ConfigError(_) => ErrorSeverity::Critical,
            IpcError::AuthenticationError(_) => ErrorSeverity::Critical,
            IpcError::PermissionError(_) => ErrorSeverity::Critical,
            IpcError::ProtocolError(_) => ErrorSeverity::High,
            IpcError::MaxReconnectAttemptsExceeded => ErrorSeverity::High,
            IpcError::ConnectionError(_) => ErrorSeverity::Medium,
            IpcError::NetworkError(_) => ErrorSeverity::Medium,
            IpcError::TimeoutError(_) => ErrorSeverity::Medium,
            IpcError::MessageHandlingError(_) => ErrorSeverity::Medium,
            IpcError::SerializationError(_) => ErrorSeverity::Low,
            IpcError::DeserializationError(_) => ErrorSeverity::Low,
            _ => ErrorSeverity::Low,
        }
    }
    
    /// 获取错误类别
    pub fn category(&self) -> ErrorCategory {
        match self {
            IpcError::ConnectionError(_) | 
            IpcError::ConnectionNotAvailable |
            IpcError::NetworkError(_) => ErrorCategory::Network,
            
            IpcError::SerializationError(_) |
            IpcError::DeserializationError(_) |
            IpcError::InvalidMessageFormat(_) => ErrorCategory::Serialization,
            
            IpcError::AuthenticationError(_) |
            IpcError::PermissionError(_) |
            IpcError::EncryptionError(_) |
            IpcError::DecryptionError(_) |
            IpcError::SignatureVerificationError(_) => ErrorCategory::Security,
            
            IpcError::ConfigError(_) => ErrorCategory::Configuration,
            
            IpcError::MessageHandlingError(_) |
            IpcError::RoutingError(_) |
            IpcError::UnsupportedMessageType(_) => ErrorCategory::MessageProcessing,
            
            IpcError::ProtocolError(_) => ErrorCategory::Protocol,
            
            IpcError::TimeoutError(_) => ErrorCategory::Timeout,
            
            _ => ErrorCategory::Other,
        }
    }
}

/// 错误严重程度
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum ErrorSeverity {
    Low,
    Medium,
    High,
    Critical,
}

impl ErrorSeverity {
    /// 转换为字符串
    pub fn as_str(&self) -> &'static str {
        match self {
            ErrorSeverity::Low => "低",
            ErrorSeverity::Medium => "中",
            ErrorSeverity::High => "高",
            ErrorSeverity::Critical => "严重",
        }
    }
}

/// 错误类别
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ErrorCategory {
    Network,
    Serialization,
    Security,
    Configuration,
    MessageProcessing,
    Protocol,
    Timeout,
    Other,
}

impl ErrorCategory {
    /// 转换为字符串
    pub fn as_str(&self) -> &'static str {
        match self {
            ErrorCategory::Network => "网络",
            ErrorCategory::Serialization => "序列化",
            ErrorCategory::Security => "安全",
            ErrorCategory::Configuration => "配置",
            ErrorCategory::MessageProcessing => "消息处理",
            ErrorCategory::Protocol => "协议",
            ErrorCategory::Timeout => "超时",
            ErrorCategory::Other => "其他",
        }
    }
}

// 为 Tauri 命令提供字符串转换
impl From<IpcError> for String {
    fn from(error: IpcError) -> Self {
        error.to_string()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_retryable() {
        assert!(IpcError::ConnectionError("test".to_string()).is_retryable());
        assert!(IpcError::NetworkError("test".to_string()).is_retryable());
        assert!(IpcError::TimeoutError("test".to_string()).is_retryable());
        assert!(!IpcError::ConfigError("test".to_string()).is_retryable());
        assert!(!IpcError::AuthenticationError("test".to_string()).is_retryable());
    }

    #[test]
    fn test_error_fatal() {
        assert!(IpcError::ConfigError("test".to_string()).is_fatal());
        assert!(IpcError::AuthenticationError("test".to_string()).is_fatal());
        assert!(IpcError::MaxReconnectAttemptsExceeded.is_fatal());
        assert!(!IpcError::ConnectionError("test".to_string()).is_fatal());
        assert!(!IpcError::TimeoutError("test".to_string()).is_fatal());
    }

    #[test]
    fn test_error_severity() {
        assert_eq!(IpcError::ConfigError("test".to_string()).severity(), ErrorSeverity::Critical);
        assert_eq!(IpcError::ConnectionError("test".to_string()).severity(), ErrorSeverity::Medium);
        assert_eq!(IpcError::SerializationError("test".to_string()).severity(), ErrorSeverity::Low);
    }

    #[test]
    fn test_error_category() {
        assert_eq!(IpcError::ConnectionError("test".to_string()).category(), ErrorCategory::Network);
        assert_eq!(IpcError::SerializationError("test".to_string()).category(), ErrorCategory::Serialization);
        assert_eq!(IpcError::AuthenticationError("test".to_string()).category(), ErrorCategory::Security);
        assert_eq!(IpcError::ConfigError("test".to_string()).category(), ErrorCategory::Configuration);
    }

    #[test]
    fn test_severity_ordering() {
        assert!(ErrorSeverity::Low < ErrorSeverity::Medium);
        assert!(ErrorSeverity::Medium < ErrorSeverity::High);
        assert!(ErrorSeverity::High < ErrorSeverity::Critical);
    }

    #[test]
    fn test_severity_as_str() {
        assert_eq!(ErrorSeverity::Low.as_str(), "低");
        assert_eq!(ErrorSeverity::Medium.as_str(), "中");
        assert_eq!(ErrorSeverity::High.as_str(), "高");
        assert_eq!(ErrorSeverity::Critical.as_str(), "严重");
    }

    #[test]
    fn test_category_as_str() {
        assert_eq!(ErrorCategory::Network.as_str(), "网络");
        assert_eq!(ErrorCategory::Security.as_str(), "安全");
        assert_eq!(ErrorCategory::Configuration.as_str(), "配置");
    }
}
