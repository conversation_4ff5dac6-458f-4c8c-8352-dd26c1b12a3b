//! Tauri IPC 命令集成模块

use crate::ipc::{TauriIpcReceiver, IpcMessage};
use serde::{Deserialize, Serialize};
use tauri::Manager;

/// 凭证数据结构
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Credential {
    pub id: Option<String>,
    pub domain: String,
    pub username: String,
    pub password: String,
    pub notes: Option<String>,
    pub created_at: Option<i64>,
    pub updated_at: Option<i64>,
}

/// 应用设置数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppSettings {
    pub auto_lock_timeout: u32,
    pub enable_biometric: bool,
    pub sync_enabled: bool,
    pub theme: String,
    pub language: String,
}

/// 认证请求数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthRequest {
    pub auth_type: String,
    pub challenge: Option<String>,
    pub credentials: Option<serde_json::Value>,
}

/// 认证响应数据结构
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AuthResponse {
    pub authenticated: bool,
    pub session_id: Option<String>,
    pub expires_at: Option<i64>,
    pub error: Option<String>,
}

/// Tauri 命令：获取凭证
#[tauri::command]
pub async fn get_credentials(
    app_handle: tauri::AppHandle,
    domain: String
) -> Result<Vec<Credential>, String> {
    log::info!("Tauri 命令：获取域名 {} 的凭证", domain);
    
    // 获取 IPC 接收端状态
    let ipc_receiver = app_handle.state::<TauriIpcReceiver>();
    
    // 创建请求消息
    let request = IpcMessage::new_with_response(
        "get_credentials".to_string(),
        serde_json::json!({ "domain": domain })
    );
    
    // 发送请求到守护进程
    match ipc_receiver.send_request(&request).await {
        Ok(response) => {
            if let Ok(credentials) = serde_json::from_value::<Vec<Credential>>(response.payload) {
                Ok(credentials)
            } else {
                Err("解析凭证数据失败".to_string())
            }
        }
        Err(e) => {
            log::error!("获取凭证失败: {}", e);
            Err(format!("获取凭证失败: {}", e))
        }
    }
}

/// Tauri 命令：保存凭证
#[tauri::command]
pub async fn save_credential(
    app_handle: tauri::AppHandle,
    credential: Credential
) -> Result<String, String> {
    log::info!("Tauri 命令：保存凭证到域名 {}", credential.domain);
    
    let ipc_receiver = app_handle.state::<TauriIpcReceiver>();
    
    let request = IpcMessage::new_with_response(
        "save_credential".to_string(),
        serde_json::to_value(&credential).map_err(|e| format!("序列化凭证失败: {}", e))?
    );
    
    match ipc_receiver.send_request(&request).await {
        Ok(response) => {
            if response.payload.get("success").and_then(|v| v.as_bool()).unwrap_or(false) {
                let credential_id = response.payload.get("credential_id")
                    .and_then(|v| v.as_str())
                    .unwrap_or("unknown")
                    .to_string();
                Ok(credential_id)
            } else {
                let error_msg = response.payload.get("error")
                    .and_then(|v| v.as_str())
                    .unwrap_or("保存失败")
                    .to_string();
                Err(error_msg)
            }
        }
        Err(e) => {
            log::error!("保存凭证失败: {}", e);
            Err(format!("保存凭证失败: {}", e))
        }
    }
}

/// Tauri 命令：更新凭证
#[tauri::command]
pub async fn update_credential(
    app_handle: tauri::AppHandle,
    credential: Credential
) -> Result<(), String> {
    log::info!("Tauri 命令：更新凭证 {:?}", credential.id);
    
    let ipc_receiver = app_handle.state::<TauriIpcReceiver>();
    
    let request = IpcMessage::new_with_response(
        "update_credential".to_string(),
        serde_json::to_value(&credential).map_err(|e| format!("序列化凭证失败: {}", e))?
    );
    
    match ipc_receiver.send_request(&request).await {
        Ok(response) => {
            if response.payload.get("success").and_then(|v| v.as_bool()).unwrap_or(false) {
                Ok(())
            } else {
                let error_msg = response.payload.get("error")
                    .and_then(|v| v.as_str())
                    .unwrap_or("更新失败")
                    .to_string();
                Err(error_msg)
            }
        }
        Err(e) => {
            log::error!("更新凭证失败: {}", e);
            Err(format!("更新凭证失败: {}", e))
        }
    }
}

/// Tauri 命令：删除凭证
#[tauri::command]
pub async fn delete_credential(
    app_handle: tauri::AppHandle,
    credential_id: String
) -> Result<(), String> {
    log::info!("Tauri 命令：删除凭证 {}", credential_id);
    
    let ipc_receiver = app_handle.state::<TauriIpcReceiver>();
    
    let request = IpcMessage::new_with_response(
        "delete_credential".to_string(),
        serde_json::json!({ "id": credential_id })
    );
    
    match ipc_receiver.send_request(&request).await {
        Ok(response) => {
            if response.payload.get("success").and_then(|v| v.as_bool()).unwrap_or(false) {
                Ok(())
            } else {
                let error_msg = response.payload.get("error")
                    .and_then(|v| v.as_str())
                    .unwrap_or("删除失败")
                    .to_string();
                Err(error_msg)
            }
        }
        Err(e) => {
            log::error!("删除凭证失败: {}", e);
            Err(format!("删除凭证失败: {}", e))
        }
    }
}

/// Tauri 命令：搜索凭证
#[tauri::command]
pub async fn search_credentials(
    app_handle: tauri::AppHandle,
    query: String
) -> Result<Vec<Credential>, String> {
    log::info!("Tauri 命令：搜索凭证 '{}'", query);
    
    let ipc_receiver = app_handle.state::<TauriIpcReceiver>();
    
    let request = IpcMessage::new_with_response(
        "search_credentials".to_string(),
        serde_json::json!({ "query": query })
    );
    
    match ipc_receiver.send_request(&request).await {
        Ok(response) => {
            if let Ok(results) = serde_json::from_value::<Vec<Credential>>(
                response.payload.get("results").unwrap_or(&serde_json::Value::Array(vec![])).clone()
            ) {
                Ok(results)
            } else {
                Err("解析搜索结果失败".to_string())
            }
        }
        Err(e) => {
            log::error!("搜索凭证失败: {}", e);
            Err(format!("搜索凭证失败: {}", e))
        }
    }
}

/// Tauri 命令：获取应用设置
#[tauri::command]
pub async fn get_app_settings(
    app_handle: tauri::AppHandle
) -> Result<AppSettings, String> {
    log::info!("Tauri 命令：获取应用设置");
    
    let ipc_receiver = app_handle.state::<TauriIpcReceiver>();
    
    let request = IpcMessage::new_with_response(
        "get_settings".to_string(),
        serde_json::json!({})
    );
    
    match ipc_receiver.send_request(&request).await {
        Ok(response) => {
            if let Ok(settings) = serde_json::from_value::<AppSettings>(response.payload) {
                Ok(settings)
            } else {
                Err("解析设置数据失败".to_string())
            }
        }
        Err(e) => {
            log::error!("获取设置失败: {}", e);
            Err(format!("获取设置失败: {}", e))
        }
    }
}

/// Tauri 命令：更新应用设置
#[tauri::command]
pub async fn update_app_settings(
    app_handle: tauri::AppHandle,
    settings: AppSettings
) -> Result<(), String> {
    log::info!("Tauri 命令：更新应用设置");
    
    let ipc_receiver = app_handle.state::<TauriIpcReceiver>();
    
    let request = IpcMessage::new_with_response(
        "update_settings".to_string(),
        serde_json::to_value(&settings).map_err(|e| format!("序列化设置失败: {}", e))?
    );
    
    match ipc_receiver.send_request(&request).await {
        Ok(response) => {
            if response.payload.get("success").and_then(|v| v.as_bool()).unwrap_or(false) {
                Ok(())
            } else {
                let error_msg = response.payload.get("error")
                    .and_then(|v| v.as_str())
                    .unwrap_or("更新设置失败")
                    .to_string();
                Err(error_msg)
            }
        }
        Err(e) => {
            log::error!("更新设置失败: {}", e);
            Err(format!("更新设置失败: {}", e))
        }
    }
}

/// Tauri 命令：执行认证
#[tauri::command]
pub async fn authenticate(
    app_handle: tauri::AppHandle,
    auth_request: AuthRequest
) -> Result<AuthResponse, String> {
    log::info!("Tauri 命令：执行认证 (类型: {})", auth_request.auth_type);
    
    let ipc_receiver = app_handle.state::<TauriIpcReceiver>();
    
    let request = IpcMessage::new_with_response(
        "auth_request".to_string(),
        serde_json::to_value(&auth_request).map_err(|e| format!("序列化认证请求失败: {}", e))?
    );
    
    match ipc_receiver.send_request(&request).await {
        Ok(response) => {
            if let Ok(auth_response) = serde_json::from_value::<AuthResponse>(response.payload) {
                Ok(auth_response)
            } else {
                Err("解析认证响应失败".to_string())
            }
        }
        Err(e) => {
            log::error!("认证失败: {}", e);
            Err(format!("认证失败: {}", e))
        }
    }
}

/// Tauri 命令：健康检查
#[tauri::command]
pub async fn health_check(
    app_handle: tauri::AppHandle
) -> Result<serde_json::Value, String> {
    log::debug!("Tauri 命令：健康检查");
    
    let ipc_receiver = app_handle.state::<TauriIpcReceiver>();
    
    let request = IpcMessage::new_with_response(
        "health_check".to_string(),
        serde_json::json!({
            "timestamp": chrono::Utc::now().timestamp(),
            "source": "tauri_command"
        })
    );
    
    match ipc_receiver.send_request(&request).await {
        Ok(response) => Ok(response.payload),
        Err(e) => {
            log::error!("健康检查失败: {}", e);
            Err(format!("健康检查失败: {}", e))
        }
    }
}

/// Tauri 命令：获取 IPC 状态
#[tauri::command]
pub async fn get_ipc_status(
    app_handle: tauri::AppHandle
) -> Result<serde_json::Value, String> {
    log::debug!("Tauri 命令：获取 IPC 状态");
    
    let ipc_receiver = app_handle.state::<TauriIpcReceiver>();
    
    let status = serde_json::json!({
        "is_running": ipc_receiver.is_running().await,
        "supported_message_types": ipc_receiver.get_supported_message_types(),
        "config": {
            "daemon_address": ipc_receiver.get_config().daemon_address,
            "health_check_interval": ipc_receiver.get_config().health_check_interval.as_secs(),
            "max_concurrent_messages": ipc_receiver.get_config().max_concurrent_messages,
            "enable_message_queue": ipc_receiver.get_config().enable_message_queue,
        }
    });
    
    Ok(status)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_credential_serialization() {
        let credential = Credential {
            id: Some("test_id".to_string()),
            domain: "example.com".to_string(),
            username: "<EMAIL>".to_string(),
            password: "password123".to_string(),
            notes: Some("Test notes".to_string()),
            created_at: Some(**********),
            updated_at: Some(**********),
        };
        
        let json = serde_json::to_value(&credential).unwrap();
        let deserialized: Credential = serde_json::from_value(json).unwrap();
        
        assert_eq!(credential.id, deserialized.id);
        assert_eq!(credential.domain, deserialized.domain);
        assert_eq!(credential.username, deserialized.username);
    }

    #[test]
    fn test_app_settings_serialization() {
        let settings = AppSettings {
            auto_lock_timeout: 600,
            enable_biometric: true,
            sync_enabled: false,
            theme: "dark".to_string(),
            language: "zh-CN".to_string(),
        };
        
        let json = serde_json::to_value(&settings).unwrap();
        let deserialized: AppSettings = serde_json::from_value(json).unwrap();
        
        assert_eq!(settings.auto_lock_timeout, deserialized.auto_lock_timeout);
        assert_eq!(settings.enable_biometric, deserialized.enable_biometric);
        assert_eq!(settings.theme, deserialized.theme);
    }

    #[test]
    fn test_auth_request_serialization() {
        let auth_request = AuthRequest {
            auth_type: "password".to_string(),
            challenge: Some("challenge_data".to_string()),
            credentials: Some(serde_json::json!({"password": "test123"})),
        };
        
        let json = serde_json::to_value(&auth_request).unwrap();
        let deserialized: AuthRequest = serde_json::from_value(json).unwrap();
        
        assert_eq!(auth_request.auth_type, deserialized.auth_type);
        assert_eq!(auth_request.challenge, deserialized.challenge);
    }
}
