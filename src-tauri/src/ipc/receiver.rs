//! Tauri 主应用 IPC 接收端实现

use crate::ipc::{
    IpcReceiverConfig, DaemonIpcClient, DaemonConnectionConfig, MessageRouter,
    IpcMessage, IpcResponse, IpcError, IpcResult
};
use crate::ipc::router::{LoggingMiddleware, AuthenticationMiddleware, RateLimitMiddleware};
use std::sync::Arc;
use tokio::sync::{Mutex, mpsc};
use tokio::time::{interval, Duration};

/// Tauri 主应用 IPC 接收端
pub struct TauriIpcReceiver {
    config: IpcReceiverConfig,
    daemon_client: Arc<DaemonIpcClient>,
    message_router: MessageRouter,
    app_handle: tauri::AppHandle,
    is_running: Arc<Mutex<bool>>,
    message_queue: Option<mpsc::UnboundedSender<IpcMessage>>,
    shutdown_sender: Option<mpsc::UnboundedSender<()>>,
}

impl TauriIpcReceiver {
    /// 初始化 IPC 接收端
    pub async fn initialize(app_handle: tauri::AppHandle) -> IpcResult<Self> {
        log::info!("初始化 Tauri IPC 接收端...");
        
        // 加载配置
        let config = IpcReceiverConfig::load().await?;
        config.validate()?;
        
        // 建立与守护进程的连接
        let daemon_config = DaemonConnectionConfig {
            address: config.daemon_address.clone(),
            timeout: config.connection_timeout,
            max_reconnect_attempts: config.max_reconnect_attempts,
            reconnect_delay: config.reconnect_delay,
            message_timeout: config.message_timeout,
            enable_heartbeat: true,
            heartbeat_interval: config.health_check_interval,
        };
        
        let daemon_client = Arc::new(
            DaemonIpcClient::connect_with_config(daemon_config).await?
        );
        
        // 初始化消息路由器
        let mut message_router = MessageRouter::new();
        
        // 添加中间件
        message_router.add_middleware(Box::new(LoggingMiddleware::new()));
        
        if config.verify_daemon_signature {
            message_router.add_middleware(Box::new(AuthenticationMiddleware::new()));
        }
        
        message_router.add_middleware(Box::new(RateLimitMiddleware::new(
            config.max_concurrent_messages as u32
        )));
        
        // 创建消息队列（如果启用）
        let message_queue = if config.enable_message_queue {
            let (sender, _receiver) = mpsc::unbounded_channel();
            Some(sender)
        } else {
            None
        };
        
        Ok(Self {
            config,
            daemon_client,
            message_router,
            app_handle,
            is_running: Arc::new(Mutex::new(false)),
            message_queue,
            shutdown_sender: None,
        })
    }
    
    /// 启动 IPC 接收端
    pub async fn start(&mut self) -> IpcResult<()> {
        log::info!("启动 Tauri IPC 接收端...");
        
        // 检查是否已经在运行
        {
            let mut running = self.is_running.lock().await;
            if *running {
                return Err(IpcError::InternalError("IPC 接收端已在运行".to_string()));
            }
            *running = true;
        }
        
        // 创建关闭信号通道
        let (shutdown_sender, mut shutdown_receiver) = mpsc::unbounded_channel();
        self.shutdown_sender = Some(shutdown_sender);
        
        // 启动守护进程连接监听
        self.start_daemon_listener().await?;
        
        // 启动健康检查
        self.start_health_check().await?;
        
        // 启动消息队列处理器（如果启用）
        if self.config.enable_message_queue {
            self.start_message_queue_processor().await?;
        }
        
        // 等待关闭信号
        let is_running = self.is_running.clone();
        tokio::spawn(async move {
            shutdown_receiver.recv().await;
            let mut running = is_running.lock().await;
            *running = false;
            log::info!("IPC 接收端已停止");
        });
        
        log::info!("Tauri IPC 接收端已启动");
        Ok(())
    }
    
    /// 停止 IPC 接收端
    pub async fn stop(&self) -> IpcResult<()> {
        log::info!("停止 Tauri IPC 接收端...");
        
        if let Some(ref sender) = self.shutdown_sender {
            sender.send(()).map_err(|_| {
                IpcError::InternalError("发送关闭信号失败".to_string())
            })?;
        }
        
        // 关闭守护进程连接
        self.daemon_client.close().await?;
        
        Ok(())
    }
    
    /// 启动守护进程连接监听
    async fn start_daemon_listener(&self) -> IpcResult<()> {
        let daemon_client = self.daemon_client.clone();
        let message_router = self.message_router.clone();
        let app_handle = self.app_handle.clone();
        let is_running = self.is_running.clone();
        let message_queue = self.message_queue.clone();
        
        tokio::spawn(async move {
            loop {
                // 检查是否应该继续运行
                {
                    let running = is_running.lock().await;
                    if !*running {
                        break;
                    }
                }
                
                match daemon_client.receive_message().await {
                    Ok(message) => {
                        log::debug!("收到守护进程消息: {}", message.message_type);
                        
                        // 如果启用了消息队列，将消息放入队列
                        if let Some(ref queue_sender) = message_queue {
                            if let Err(e) = queue_sender.send(message) {
                                log::error!("消息入队失败: {}", e);
                            }
                        } else {
                            // 直接处理消息
                            Self::process_message(
                                message,
                                &message_router,
                                &app_handle,
                                &daemon_client
                            ).await;
                        }
                    }
                    Err(e) => {
                        log::error!("接收守护进程消息失败: {}", e);
                        
                        // 如果是可重试的错误，尝试重连
                        if e.is_retryable() {
                            if let Err(reconnect_err) = daemon_client.reconnect().await {
                                log::error!("重连守护进程失败: {}", reconnect_err);
                                
                                // 如果重连失败且是致命错误，退出循环
                                if reconnect_err.is_fatal() {
                                    break;
                                }
                            }
                        } else if e.is_fatal() {
                            log::error!("遇到致命错误，停止监听: {}", e);
                            break;
                        }
                        
                        // 等待一段时间后重试
                        tokio::time::sleep(Duration::from_secs(1)).await;
                    }
                }
            }
            
            log::info!("守护进程监听器已停止");
        });
        
        Ok(())
    }
    
    /// 处理单个消息
    async fn process_message(
        message: IpcMessage,
        message_router: &MessageRouter,
        app_handle: &tauri::AppHandle,
        daemon_client: &Arc<DaemonIpcClient>
    ) {
        let start_time = std::time::Instant::now();
        
        // 路由消息到相应的处理器
        match message_router.route_message(&message, app_handle).await {
            Ok(response) => {
                log::debug!(
                    "消息处理成功: {} (耗时: {}ms)",
                    message.message_id,
                    start_time.elapsed().as_millis()
                );
                
                // 如果消息需要响应，发送响应回守护进程
                if message.response_required {
                    if let Err(e) = daemon_client.send_response(&response).await {
                        log::error!("发送响应失败: {}", e);
                    }
                }
            }
            Err(e) => {
                log::error!(
                    "消息处理失败: {} (错误: {}, 耗时: {}ms)",
                    message.message_id,
                    e,
                    start_time.elapsed().as_millis()
                );
                
                // 发送错误响应
                if message.response_required {
                    let error_response = IpcResponse::error(
                        &message.message_id,
                        &format!("处理失败: {}", e)
                    );
                    
                    if let Err(send_err) = daemon_client.send_response(&error_response).await {
                        log::error!("发送错误响应失败: {}", send_err);
                    }
                }
            }
        }
    }
    
    /// 启动健康检查
    async fn start_health_check(&self) -> IpcResult<()> {
        let daemon_client = self.daemon_client.clone();
        let check_interval = self.config.health_check_interval;
        let is_running = self.is_running.clone();
        
        tokio::spawn(async move {
            let mut interval = interval(check_interval);
            
            loop {
                interval.tick().await;
                
                // 检查是否应该继续运行
                {
                    let running = is_running.lock().await;
                    if !*running {
                        break;
                    }
                }
                
                // 发送健康检查消息到守护进程
                let health_check = IpcMessage::new(
                    "health_check".to_string(),
                    serde_json::json!({
                        "timestamp": chrono::Utc::now().timestamp(),
                        "source": "tauri_app"
                    })
                );
                
                match daemon_client.send_message(&health_check).await {
                    Ok(_) => {
                        log::debug!("健康检查消息已发送");
                    }
                    Err(e) => {
                        log::error!("健康检查失败: {}", e);
                        
                        // 如果健康检查失败，尝试重连
                        if e.is_retryable() {
                            if let Err(reconnect_err) = daemon_client.reconnect().await {
                                log::error!("健康检查重连失败: {}", reconnect_err);
                            }
                        }
                    }
                }
            }
            
            log::info!("健康检查已停止");
        });
        
        Ok(())
    }
    
    /// 启动消息队列处理器
    async fn start_message_queue_processor(&self) -> IpcResult<()> {
        if let Some(ref queue_sender) = self.message_queue {
            let (_sender, mut receiver) = mpsc::unbounded_channel();
            // 这里需要重新创建接收端，因为我们只存储了发送端
            // 实际实现中应该在初始化时同时存储发送端和接收端
            
            let message_router = self.message_router.clone();
            let app_handle = self.app_handle.clone();
            let daemon_client = self.daemon_client.clone();
            let is_running = self.is_running.clone();
            
            tokio::spawn(async move {
                while let Some(message) = receiver.recv().await {
                    // 检查是否应该继续运行
                    {
                        let running = is_running.lock().await;
                        if !*running {
                            break;
                        }
                    }
                    
                    Self::process_message(
                        message,
                        &message_router,
                        &app_handle,
                        &daemon_client
                    ).await;
                }
                
                log::info!("消息队列处理器已停止");
            });
        }
        
        Ok(())
    }
    
    /// 检查是否正在运行
    pub async fn is_running(&self) -> bool {
        let running = self.is_running.lock().await;
        *running
    }
    
    /// 获取配置
    pub fn get_config(&self) -> &IpcReceiverConfig {
        &self.config
    }
    
    /// 获取支持的消息类型
    pub fn get_supported_message_types(&self) -> Vec<String> {
        self.message_router.get_supported_message_types()
    }

    /// 发送请求到守护进程并等待响应
    pub async fn send_request(&self, message: &IpcMessage) -> IpcResult<IpcResponse> {
        self.daemon_client.send_request(message).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_receiver_config() {
        let config = IpcReceiverConfig::default();
        assert!(config.validate().is_ok());
        assert!(!config.daemon_address.is_empty());
        assert!(config.max_reconnect_attempts > 0);
    }

    // 注意：由于需要实际的 Tauri 应用句柄，大部分测试需要在集成测试中进行
}
