//! IPC 协议定义模块

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// IPC 消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IpcMessage {
    /// 消息ID（用于请求-响应匹配）
    pub message_id: String,
    /// 消息类型
    pub message_type: String,
    /// 消息负载
    pub payload: serde_json::Value,
    /// 时间戳
    pub timestamp: u64,
    /// 是否需要响应
    pub response_required: bool,
    /// 消息来源
    pub source: Option<String>,
    /// 消息目标
    pub target: Option<String>,
    /// 消息优先级
    pub priority: MessagePriority,
    /// 消息元数据
    pub metadata: HashMap<String, String>,
}

impl IpcMessage {
    /// 创建新的 IPC 消息
    pub fn new(message_type: String, payload: serde_json::Value) -> Self {
        Self {
            message_id: uuid::Uuid::new_v4().to_string(),
            message_type,
            payload,
            timestamp: chrono::Utc::now().timestamp_millis() as u64,
            response_required: false,
            source: None,
            target: None,
            priority: MessagePriority::Normal,
            metadata: HashMap::new(),
        }
    }
    
    /// 创建需要响应的消息
    pub fn new_with_response(message_type: String, payload: serde_json::Value) -> Self {
        let mut message = Self::new(message_type, payload);
        message.response_required = true;
        message
    }
    
    /// 设置消息来源
    pub fn with_source(mut self, source: String) -> Self {
        self.source = Some(source);
        self
    }
    
    /// 设置消息目标
    pub fn with_target(mut self, target: String) -> Self {
        self.target = Some(target);
        self
    }
    
    /// 设置消息优先级
    pub fn with_priority(mut self, priority: MessagePriority) -> Self {
        self.priority = priority;
        self
    }
    
    /// 添加元数据
    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }
    
    /// 验证消息格式
    pub fn validate(&self) -> Result<(), String> {
        if self.message_id.is_empty() {
            return Err("消息ID不能为空".to_string());
        }
        
        if self.message_type.is_empty() {
            return Err("消息类型不能为空".to_string());
        }
        
        if self.timestamp == 0 {
            return Err("时间戳不能为0".to_string());
        }
        
        Ok(())
    }
    
    /// 检查消息是否过期
    pub fn is_expired(&self, timeout_ms: u64) -> bool {
        let now = chrono::Utc::now().timestamp_millis() as u64;
        now > self.timestamp + timeout_ms
    }
}

/// IPC 响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IpcResponse {
    /// 响应ID（对应请求的消息ID）
    pub request_id: String,
    /// 响应状态
    pub status: ResponseStatus,
    /// 响应负载
    pub payload: serde_json::Value,
    /// 错误信息（如果有）
    pub error: Option<String>,
    /// 时间戳
    pub timestamp: u64,
    /// 处理时间（毫秒）
    pub processing_time_ms: Option<u64>,
    /// 响应元数据
    pub metadata: HashMap<String, String>,
}

impl IpcResponse {
    /// 创建成功响应
    pub fn success(request_id: String, payload: serde_json::Value) -> Self {
        Self {
            request_id,
            status: ResponseStatus::Success,
            payload,
            error: None,
            timestamp: chrono::Utc::now().timestamp_millis() as u64,
            processing_time_ms: None,
            metadata: HashMap::new(),
        }
    }
    
    /// 创建错误响应
    pub fn error(request_id: &str, error_message: &str) -> Self {
        Self {
            request_id: request_id.to_string(),
            status: ResponseStatus::Error,
            payload: serde_json::Value::Null,
            error: Some(error_message.to_string()),
            timestamp: chrono::Utc::now().timestamp_millis() as u64,
            processing_time_ms: None,
            metadata: HashMap::new(),
        }
    }
    
    /// 设置处理时间
    pub fn with_processing_time(mut self, processing_time_ms: u64) -> Self {
        self.processing_time_ms = Some(processing_time_ms);
        self
    }
    
    /// 添加元数据
    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }
}

/// 消息优先级
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum MessagePriority {
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3,
}

impl Default for MessagePriority {
    fn default() -> Self {
        MessagePriority::Normal
    }
}

/// 响应状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum ResponseStatus {
    Success,
    Error,
    Timeout,
    Cancelled,
}

/// IPC 消息类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum IpcMessageType {
    // 凭证管理
    GetCredentials,
    SaveCredential,
    UpdateCredential,
    DeleteCredential,
    SearchCredentials,
    
    // 设置管理
    GetSettings,
    UpdateSettings,
    ResetSettings,
    
    // 认证相关
    AuthRequest,
    AuthResponse,
    AuthChallenge,
    
    // 健康检查
    HealthCheck,
    HealthResponse,
    
    // 系统控制
    Ping,
    Pong,
    Shutdown,
    Restart,
    
    // 同步相关
    SyncRequest,
    SyncResponse,
    SyncStatus,
    
    // 通知消息
    Notification,
    Alert,
    
    // 自定义消息
    Custom(String),
}

impl IpcMessageType {
    /// 转换为字符串
    pub fn as_str(&self) -> &str {
        match self {
            IpcMessageType::GetCredentials => "get_credentials",
            IpcMessageType::SaveCredential => "save_credential",
            IpcMessageType::UpdateCredential => "update_credential",
            IpcMessageType::DeleteCredential => "delete_credential",
            IpcMessageType::SearchCredentials => "search_credentials",
            IpcMessageType::GetSettings => "get_settings",
            IpcMessageType::UpdateSettings => "update_settings",
            IpcMessageType::ResetSettings => "reset_settings",
            IpcMessageType::AuthRequest => "auth_request",
            IpcMessageType::AuthResponse => "auth_response",
            IpcMessageType::AuthChallenge => "auth_challenge",
            IpcMessageType::HealthCheck => "health_check",
            IpcMessageType::HealthResponse => "health_response",
            IpcMessageType::Ping => "ping",
            IpcMessageType::Pong => "pong",
            IpcMessageType::Shutdown => "shutdown",
            IpcMessageType::Restart => "restart",
            IpcMessageType::SyncRequest => "sync_request",
            IpcMessageType::SyncResponse => "sync_response",
            IpcMessageType::SyncStatus => "sync_status",
            IpcMessageType::Notification => "notification",
            IpcMessageType::Alert => "alert",
            IpcMessageType::Custom(s) => s,
        }
    }
    
    /// 从字符串创建
    pub fn from_str(s: &str) -> Self {
        match s {
            "get_credentials" => IpcMessageType::GetCredentials,
            "save_credential" => IpcMessageType::SaveCredential,
            "update_credential" => IpcMessageType::UpdateCredential,
            "delete_credential" => IpcMessageType::DeleteCredential,
            "search_credentials" => IpcMessageType::SearchCredentials,
            "get_settings" => IpcMessageType::GetSettings,
            "update_settings" => IpcMessageType::UpdateSettings,
            "reset_settings" => IpcMessageType::ResetSettings,
            "auth_request" => IpcMessageType::AuthRequest,
            "auth_response" => IpcMessageType::AuthResponse,
            "auth_challenge" => IpcMessageType::AuthChallenge,
            "health_check" => IpcMessageType::HealthCheck,
            "health_response" => IpcMessageType::HealthResponse,
            "ping" => IpcMessageType::Ping,
            "pong" => IpcMessageType::Pong,
            "shutdown" => IpcMessageType::Shutdown,
            "restart" => IpcMessageType::Restart,
            "sync_request" => IpcMessageType::SyncRequest,
            "sync_response" => IpcMessageType::SyncResponse,
            "sync_status" => IpcMessageType::SyncStatus,
            "notification" => IpcMessageType::Notification,
            "alert" => IpcMessageType::Alert,
            _ => IpcMessageType::Custom(s.to_string()),
        }
    }
    
    /// 检查是否需要认证
    pub fn requires_auth(&self) -> bool {
        match self {
            IpcMessageType::GetCredentials |
            IpcMessageType::SaveCredential |
            IpcMessageType::UpdateCredential |
            IpcMessageType::DeleteCredential |
            IpcMessageType::SearchCredentials |
            IpcMessageType::GetSettings |
            IpcMessageType::UpdateSettings |
            IpcMessageType::ResetSettings |
            IpcMessageType::SyncRequest => true,
            _ => false,
        }
    }
    
    /// 获取默认超时时间（毫秒）
    pub fn default_timeout_ms(&self) -> u64 {
        match self {
            IpcMessageType::HealthCheck | IpcMessageType::Ping => 5000,
            IpcMessageType::GetCredentials | IpcMessageType::SearchCredentials => 10000,
            IpcMessageType::SaveCredential | IpcMessageType::UpdateCredential => 15000,
            IpcMessageType::SyncRequest => 30000,
            _ => 10000,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_ipc_message_creation() {
        let payload = serde_json::json!({"test": "data"});
        let message = IpcMessage::new("test_type".to_string(), payload.clone());
        
        assert!(!message.message_id.is_empty());
        assert_eq!(message.message_type, "test_type");
        assert_eq!(message.payload, payload);
        assert!(!message.response_required);
        assert_eq!(message.priority, MessagePriority::Normal);
    }

    #[test]
    fn test_ipc_message_with_response() {
        let payload = serde_json::json!({"test": "data"});
        let message = IpcMessage::new_with_response("test_type".to_string(), payload);
        
        assert!(message.response_required);
    }

    #[test]
    fn test_ipc_message_validation() {
        let mut message = IpcMessage::new("test".to_string(), serde_json::Value::Null);
        assert!(message.validate().is_ok());
        
        message.message_id = String::new();
        assert!(message.validate().is_err());
        
        message.message_id = "test".to_string();
        message.message_type = String::new();
        assert!(message.validate().is_err());
    }

    #[test]
    fn test_ipc_response_creation() {
        let payload = serde_json::json!({"result": "success"});
        let response = IpcResponse::success("test_id".to_string(), payload.clone());
        
        assert_eq!(response.request_id, "test_id");
        assert_eq!(response.status, ResponseStatus::Success);
        assert_eq!(response.payload, payload);
        assert!(response.error.is_none());
    }

    #[test]
    fn test_error_response() {
        let response = IpcResponse::error("test_id", "test error");
        
        assert_eq!(response.request_id, "test_id");
        assert_eq!(response.status, ResponseStatus::Error);
        assert_eq!(response.error, Some("test error".to_string()));
    }

    #[test]
    fn test_message_priority_ordering() {
        assert!(MessagePriority::Low < MessagePriority::Normal);
        assert!(MessagePriority::Normal < MessagePriority::High);
        assert!(MessagePriority::High < MessagePriority::Critical);
    }

    #[test]
    fn test_message_type_conversion() {
        let msg_type = IpcMessageType::GetCredentials;
        assert_eq!(msg_type.as_str(), "get_credentials");
        assert_eq!(IpcMessageType::from_str("get_credentials"), msg_type);
    }

    #[test]
    fn test_message_type_auth_requirement() {
        assert!(IpcMessageType::GetCredentials.requires_auth());
        assert!(IpcMessageType::SaveCredential.requires_auth());
        assert!(!IpcMessageType::HealthCheck.requires_auth());
        assert!(!IpcMessageType::Ping.requires_auth());
    }

    #[test]
    fn test_message_type_timeout() {
        assert_eq!(IpcMessageType::HealthCheck.default_timeout_ms(), 5000);
        assert_eq!(IpcMessageType::GetCredentials.default_timeout_ms(), 10000);
        assert_eq!(IpcMessageType::SyncRequest.default_timeout_ms(), 30000);
    }
}
