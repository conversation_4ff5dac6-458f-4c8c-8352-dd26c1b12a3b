//! Tauri 主应用 IPC 接收端模块
//! 
//! 提供与守护进程的双向通信能力，支持：
//! - 与守护进程的 IPC 连接管理
//! - 消息路由和处理
//! - 错误处理和重连机制
//! - 健康检查和监控

pub mod receiver;
pub mod client;
pub mod handlers;
pub mod router;
pub mod protocol;
pub mod error;
pub mod commands;

#[cfg(test)]
mod tests;

// 重新导出主要类型
pub use receiver::TauriIpcReceiver;
pub use client::{DaemonIpcClient, DaemonConnectionConfig};
pub use handlers::{IpcMessageHandler, CredentialsHandler, SettingsHandler, AuthHandler, HealthHandler};
pub use router::MessageRouter;
pub use protocol::{IpcMessage, IpcResponse, IpcMessageType};
pub use error::{IpcError, IpcResult};
pub use commands::{Credential, AppSettings, AuthRequest, AuthResponse};

/// IPC 模块版本
pub const VERSION: &str = "1.0.0";

/// 默认配置常量
pub mod defaults {
    use std::time::Duration;

    /// 默认守护进程地址
    pub const DEFAULT_DAEMON_ADDRESS: &str = "127.0.0.1:8080";
    
    /// 默认连接超时时间
    pub const DEFAULT_CONNECTION_TIMEOUT: Duration = Duration::from_secs(30);
    
    /// 默认健康检查间隔
    pub const DEFAULT_HEALTH_CHECK_INTERVAL: Duration = Duration::from_secs(30);
    
    /// 默认最大重连尝试次数
    pub const DEFAULT_MAX_RECONNECT_ATTEMPTS: u32 = 5;
    
    /// 默认重连延迟
    pub const DEFAULT_RECONNECT_DELAY: Duration = Duration::from_secs(2);
    
    /// 默认消息超时时间
    pub const DEFAULT_MESSAGE_TIMEOUT: Duration = Duration::from_secs(10);
    
    /// 默认最大并发消息数
    pub const DEFAULT_MAX_CONCURRENT_MESSAGES: usize = 100;
    
    /// 默认消息队列大小
    pub const DEFAULT_QUEUE_SIZE: usize = 1000;
}

/// IPC 接收端配置
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct IpcReceiverConfig {
    /// 守护进程地址
    pub daemon_address: String,
    /// 连接超时时间
    pub connection_timeout: std::time::Duration,
    /// 健康检查间隔
    pub health_check_interval: std::time::Duration,
    /// 最大重连尝试次数
    pub max_reconnect_attempts: u32,
    /// 重连延迟
    pub reconnect_delay: std::time::Duration,
    /// 消息处理超时时间
    pub message_timeout: std::time::Duration,
    /// 最大并发消息数
    pub max_concurrent_messages: usize,
    /// 启用消息队列
    pub enable_message_queue: bool,
    /// 队列大小
    pub queue_size: usize,
    /// 启用消息加密
    pub enable_message_encryption: bool,
    /// 验证守护进程签名
    pub verify_daemon_signature: bool,
    /// 允许的消息类型
    pub allowed_message_types: Vec<String>,
}

impl Default for IpcReceiverConfig {
    fn default() -> Self {
        Self {
            daemon_address: defaults::DEFAULT_DAEMON_ADDRESS.to_string(),
            connection_timeout: defaults::DEFAULT_CONNECTION_TIMEOUT,
            health_check_interval: defaults::DEFAULT_HEALTH_CHECK_INTERVAL,
            max_reconnect_attempts: defaults::DEFAULT_MAX_RECONNECT_ATTEMPTS,
            reconnect_delay: defaults::DEFAULT_RECONNECT_DELAY,
            message_timeout: defaults::DEFAULT_MESSAGE_TIMEOUT,
            max_concurrent_messages: defaults::DEFAULT_MAX_CONCURRENT_MESSAGES,
            enable_message_queue: true,
            queue_size: defaults::DEFAULT_QUEUE_SIZE,
            enable_message_encryption: true,
            verify_daemon_signature: true,
            allowed_message_types: vec![
                "get_credentials".to_string(),
                "save_credential".to_string(),
                "delete_credential".to_string(),
                "get_settings".to_string(),
                "update_settings".to_string(),
                "health_check".to_string(),
                "auth_request".to_string(),
                "auth_response".to_string(),
            ],
        }
    }
}

impl IpcReceiverConfig {
    /// 从配置文件加载
    pub async fn load() -> IpcResult<Self> {
        // TODO: 实现从配置文件加载逻辑
        // 目前返回默认配置
        Ok(Self::default())
    }
    
    /// 验证配置
    pub fn validate(&self) -> IpcResult<()> {
        if self.daemon_address.is_empty() {
            return Err(IpcError::ConfigError("守护进程地址不能为空".to_string()));
        }
        
        if self.max_reconnect_attempts == 0 {
            return Err(IpcError::ConfigError("最大重连尝试次数必须大于0".to_string()));
        }
        
        if self.max_concurrent_messages == 0 {
            return Err(IpcError::ConfigError("最大并发消息数必须大于0".to_string()));
        }
        
        if self.enable_message_queue && self.queue_size == 0 {
            return Err(IpcError::ConfigError("启用消息队列时队列大小必须大于0".to_string()));
        }
        
        Ok(())
    }
}

/// 设置 IPC 接收端
///
/// 在 Tauri 应用初始化时调用，建立与守护进程的连接
pub async fn setup_ipc_receiver(app_handle: tauri::AppHandle) -> IpcResult<()> {
    use tauri::Manager;

    log::info!("初始化 Tauri IPC 接收端...");

    // 初始化 IPC 接收端
    let mut receiver = TauriIpcReceiver::initialize(app_handle.clone()).await?;

    // 启动接收端
    receiver.start().await?;

    // 将接收端存储到应用状态中
    app_handle.manage(receiver);

    log::info!("Tauri IPC 接收端初始化完成");
    Ok(())
}


