//! IPC 消息路由器模块

use crate::ipc::{IpcMessage, IpcResponse, IpcError, IpcResult};
use crate::ipc::handlers::{
    IpcMessageHandler, Credentials<PERSON><PERSON>ler, <PERSON>tings<PERSON><PERSON><PERSON>, 
    <PERSON>th<PERSON><PERSON><PERSON>, HealthHandler, HandlerRegistry
};
use std::collections::HashMap;
use std::sync::Arc;

/// 消息路由器
pub struct MessageRouter {
    handlers: HashMap<String, Box<dyn IpcMessageHandler>>,
    registry: HandlerRegistry,
    default_handler: Option<Box<dyn IpcMessageHandler>>,
    middleware: Vec<Box<dyn MessageMiddleware>>,
}

impl MessageRouter {
    /// 创建新的消息路由器
    pub fn new() -> Self {
        let mut router = Self {
            handlers: HashMap::new(),
            registry: HandlerRegistry::new(),
            default_handler: None,
            middleware: Vec::new(),
        };
        
        // 注册默认处理器
        router.register_default_handlers();
        router
    }
    
    /// 注册处理器
    pub fn register_handler(&mut self, handler: Box<dyn IpcMessageHandler>) {
        let handler_name = handler.name().to_string();
        let supported_types = handler.supported_message_types();
        
        // 注册到处理器映射
        self.handlers.insert(handler_name.clone(), handler);
        
        // 注册到注册表
        self.registry.register_handler_types(&handler_name, supported_types);
    }
    
    /// 设置默认处理器
    pub fn set_default_handler(&mut self, handler: Box<dyn IpcMessageHandler>) {
        self.default_handler = Some(handler);
    }
    
    /// 添加中间件
    pub fn add_middleware(&mut self, middleware: Box<dyn MessageMiddleware>) {
        self.middleware.push(middleware);
    }
    
    /// 路由消息到相应的处理器
    pub async fn route_message(&self, message: &IpcMessage, app_handle: &tauri::AppHandle) -> IpcResult<IpcResponse> {
        // 执行前置中间件
        for middleware in &self.middleware {
            middleware.before_handle(message).await?;
        }
        
        // 查找处理器
        let handler = self.find_handler(&message.message_type)?;
        
        // 处理消息
        let start_time = std::time::Instant::now();
        let result = handler.handle_message(message, app_handle).await;
        let processing_time = start_time.elapsed().as_millis() as u64;
        
        // 执行后置中间件
        for middleware in &self.middleware {
            middleware.after_handle(message, &result).await?;
        }
        
        // 添加处理时间到响应
        match result {
            Ok(mut response) => {
                response.processing_time_ms = Some(processing_time);
                Ok(response)
            }
            Err(e) => Err(e),
        }
    }
    
    /// 查找处理器
    fn find_handler(&self, message_type: &str) -> IpcResult<&dyn IpcMessageHandler> {
        // 首先检查注册表
        if let Some(handler_name) = self.registry.get_handler_name(message_type) {
            if let Some(handler) = self.handlers.get(handler_name) {
                return Ok(handler.as_ref());
            }
        }
        
        // 如果没有找到，使用默认处理器
        if let Some(ref default_handler) = self.default_handler {
            return Ok(default_handler.as_ref());
        }
        
        // 都没有找到，返回错误
        Err(IpcError::UnsupportedMessageType(message_type.to_string()))
    }
    
    /// 获取支持的消息类型
    pub fn get_supported_message_types(&self) -> Vec<String> {
        self.registry.get_supported_message_types()
    }
    
    /// 检查是否支持指定消息类型
    pub fn supports_message_type(&self, message_type: &str) -> bool {
        self.registry.supports_message_type(message_type) || self.default_handler.is_some()
    }
    
    /// 注册默认处理器
    fn register_default_handlers(&mut self) {
        // 注册凭证处理器
        self.register_handler(Box::new(CredentialsHandler::new()));
        
        // 注册设置处理器
        self.register_handler(Box::new(SettingsHandler::new()));
        
        // 注册认证处理器
        self.register_handler(Box::new(AuthHandler::new()));
        
        // 注册健康检查处理器
        self.register_handler(Box::new(HealthHandler::new()));
    }
}

impl Default for MessageRouter {
    fn default() -> Self {
        Self::new()
    }
}

impl Clone for MessageRouter {
    fn clone(&self) -> Self {
        // 创建新的路由器并重新注册处理器
        let router = Self::new();
        
        // 复制中间件（简化实现，实际可能需要更复杂的克隆逻辑）
        // 这里暂时不复制中间件，因为 Box<dyn Trait> 不能直接克隆
        
        router
    }
}

/// 消息中间件接口
#[async_trait::async_trait]
pub trait MessageMiddleware: Send + Sync {
    /// 消息处理前执行
    async fn before_handle(&self, message: &IpcMessage) -> IpcResult<()>;
    
    /// 消息处理后执行
    async fn after_handle(&self, message: &IpcMessage, result: &IpcResult<IpcResponse>) -> IpcResult<()>;
    
    /// 中间件名称
    fn name(&self) -> &str;
}

/// 日志中间件
pub struct LoggingMiddleware;

impl LoggingMiddleware {
    pub fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl MessageMiddleware for LoggingMiddleware {
    async fn before_handle(&self, message: &IpcMessage) -> IpcResult<()> {
        log::info!(
            "处理消息: {} (ID: {}, 类型: {})",
            message.message_id,
            message.message_id,
            message.message_type
        );
        Ok(())
    }
    
    async fn after_handle(&self, message: &IpcMessage, result: &IpcResult<IpcResponse>) -> IpcResult<()> {
        match result {
            Ok(response) => {
                log::info!(
                    "消息处理成功: {} (处理时间: {:?}ms)",
                    message.message_id,
                    response.processing_time_ms.unwrap_or(0)
                );
            }
            Err(e) => {
                log::error!(
                    "消息处理失败: {} (错误: {})",
                    message.message_id,
                    e
                );
            }
        }
        Ok(())
    }
    
    fn name(&self) -> &str {
        "LoggingMiddleware"
    }
}

/// 认证中间件
pub struct AuthenticationMiddleware {
    required_auth_types: Vec<String>,
}

impl AuthenticationMiddleware {
    pub fn new() -> Self {
        Self {
            required_auth_types: vec![
                "get_credentials".to_string(),
                "save_credential".to_string(),
                "update_credential".to_string(),
                "delete_credential".to_string(),
                "search_credentials".to_string(),
                "get_settings".to_string(),
                "update_settings".to_string(),
            ],
        }
    }
    
    /// 检查消息是否需要认证
    fn requires_authentication(&self, message_type: &str) -> bool {
        self.required_auth_types.contains(&message_type.to_string())
    }
    
    /// 验证认证信息
    async fn validate_authentication(&self, message: &IpcMessage) -> IpcResult<()> {
        // 检查消息元数据中的认证信息
        if let Some(auth_token) = message.metadata.get("auth_token") {
            // TODO: 实际的认证验证逻辑
            if auth_token.is_empty() {
                return Err(IpcError::AuthenticationError("认证令牌为空".to_string()));
            }
            Ok(())
        } else {
            Err(IpcError::AuthenticationError("缺少认证令牌".to_string()))
        }
    }
}

#[async_trait::async_trait]
impl MessageMiddleware for AuthenticationMiddleware {
    async fn before_handle(&self, message: &IpcMessage) -> IpcResult<()> {
        if self.requires_authentication(&message.message_type) {
            self.validate_authentication(message).await?;
        }
        Ok(())
    }
    
    async fn after_handle(&self, _message: &IpcMessage, _result: &IpcResult<IpcResponse>) -> IpcResult<()> {
        // 认证中间件在处理后不需要做任何事情
        Ok(())
    }
    
    fn name(&self) -> &str {
        "AuthenticationMiddleware"
    }
}

/// 限流中间件
pub struct RateLimitMiddleware {
    max_requests_per_minute: u32,
    request_counts: Arc<std::sync::Mutex<HashMap<String, (u32, std::time::Instant)>>>,
}

impl RateLimitMiddleware {
    pub fn new(max_requests_per_minute: u32) -> Self {
        Self {
            max_requests_per_minute,
            request_counts: Arc::new(std::sync::Mutex::new(HashMap::new())),
        }
    }
    
    /// 检查是否超过限流
    pub fn check_rate_limit(&self, source: &str) -> IpcResult<()> {
        let mut counts = self.request_counts.lock().unwrap();
        let now = std::time::Instant::now();
        
        let (count, last_reset) = counts.entry(source.to_string())
            .or_insert((0, now));
        
        // 如果距离上次重置超过1分钟，重置计数
        if now.duration_since(*last_reset).as_secs() >= 60 {
            *count = 0;
            *last_reset = now;
        }
        
        // 检查是否超过限制
        if *count >= self.max_requests_per_minute {
            return Err(IpcError::MessageHandlingError(
                format!("请求频率过高，每分钟最多 {} 次请求", self.max_requests_per_minute)
            ));
        }
        
        *count += 1;
        Ok(())
    }
}

#[async_trait::async_trait]
impl MessageMiddleware for RateLimitMiddleware {
    async fn before_handle(&self, message: &IpcMessage) -> IpcResult<()> {
        let source = message.source.as_deref().unwrap_or("unknown");
        self.check_rate_limit(source)
    }
    
    async fn after_handle(&self, _message: &IpcMessage, _result: &IpcResult<IpcResponse>) -> IpcResult<()> {
        Ok(())
    }
    
    fn name(&self) -> &str {
        "RateLimitMiddleware"
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_message_router_creation() {
        let router = MessageRouter::new();
        assert!(router.supports_message_type("get_credentials"));
        assert!(router.supports_message_type("health_check"));
        assert!(!router.supports_message_type("unknown_type"));
    }

    #[test]
    fn test_message_router_clone() {
        let router = MessageRouter::new();
        let cloned_router = router.clone();
        
        assert!(cloned_router.supports_message_type("get_credentials"));
        assert!(cloned_router.supports_message_type("health_check"));
    }

    #[test]
    fn test_logging_middleware() {
        let middleware = LoggingMiddleware::new();
        assert_eq!(middleware.name(), "LoggingMiddleware");
    }

    #[test]
    fn test_authentication_middleware() {
        let middleware = AuthenticationMiddleware::new();
        assert_eq!(middleware.name(), "AuthenticationMiddleware");
        assert!(middleware.requires_authentication("get_credentials"));
        assert!(!middleware.requires_authentication("health_check"));
    }

    #[test]
    fn test_rate_limit_middleware() {
        let middleware = RateLimitMiddleware::new(10);
        assert_eq!(middleware.name(), "RateLimitMiddleware");
        assert_eq!(middleware.max_requests_per_minute, 10);
    }

    #[tokio::test]
    async fn test_rate_limit_check() {
        let middleware = RateLimitMiddleware::new(2);
        
        // 前两次请求应该成功
        assert!(middleware.check_rate_limit("test_source").is_ok());
        assert!(middleware.check_rate_limit("test_source").is_ok());
        
        // 第三次请求应该失败
        assert!(middleware.check_rate_limit("test_source").is_err());
    }
}
