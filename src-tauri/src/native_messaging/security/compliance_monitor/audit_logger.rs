//! 审计记录器模块
//!
//! 提供审计事件记录、日志管理和轮转功能

use crate::native_messaging::error::{NativeMessagingError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use std::time::SystemTime;
use tracing::{debug, info, warn, error};
use tokio::fs;
use tokio::io::AsyncWriteExt;

/// 审计记录器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditLoggerConfig {
    /// 启用审计记录
    pub enable_audit_logging: bool,
    /// 日志文件路径
    pub log_file_path: Option<String>,
    /// 最大日志文件大小（字节）
    pub max_log_file_size: usize,
    /// 日志轮转数量
    pub log_rotation_count: u32,
    /// 记录级别
    pub log_level: AuditLevel,
    /// 是否记录到控制台
    pub log_to_console: bool,
    /// 是否启用异步写入
    pub async_logging: bool,
}

impl Default for AuditLoggerConfig {
    fn default() -> Self {
        Self {
            enable_audit_logging: true,
            log_file_path: Some("logs/audit.log".to_string()),
            max_log_file_size: 10 * 1024 * 1024, // 10MB
            log_rotation_count: 5,
            log_level: AuditLevel::Info,
            log_to_console: true,
            async_logging: true,
        }
    }
}

/// 审计记录器
#[derive(Debug)]
pub struct AuditLogger {
    config: AuditLoggerConfig,
    event_count: u64,
    current_log_size: usize,
}

impl AuditLogger {
    /// 创建新的审计记录器
    pub fn new(config: &AuditLoggerConfig) -> Result<Self> {
        // 验证配置
        if config.enable_audit_logging && config.log_file_path.is_some() {
            let log_path = PathBuf::from(config.log_file_path.as_ref().unwrap());
            if let Some(parent) = log_path.parent() {
                if !parent.exists() {
                    return Err(NativeMessagingError::ConfigError(
                        format!("日志目录不存在: {:?}", parent)
                    ));
                }
            }
        }

        Ok(Self {
            config: config.clone(),
            event_count: 0,
            current_log_size: 0,
        })
    }

    /// 记录审计事件
    pub async fn log_event(&mut self, event: &AuditEvent) -> Result<()> {
        if !self.config.enable_audit_logging {
            return Ok(());
        }

        // 检查日志级别
        if event.level < self.config.log_level {
            return Ok(());
        }

        // 记录到控制台
        if self.config.log_to_console {
            self.log_to_console(event);
        }

        // 记录到文件
        if let Some(log_path) = self.config.log_file_path.clone() {
            if self.config.async_logging {
                self.log_to_file_async(event, &log_path).await?;
            } else {
                self.log_to_file_sync(event, &log_path).await?;
            }
        }

        self.event_count += 1;
        debug!("审计事件已记录: {} (总计: {})", event.event_id, self.event_count);
        
        Ok(())
    }

    /// 批量记录审计事件
    pub async fn log_events(&mut self, events: &[AuditEvent]) -> Result<()> {
        for event in events {
            self.log_event(event).await?;
        }
        info!("批量记录了 {} 个审计事件", events.len());
        Ok(())
    }

    /// 记录到控制台
    fn log_to_console(&self, event: &AuditEvent) {
        match event.level {
            AuditLevel::Error => error!("AUDIT[{}]: {}", event.event_type, event.description),
            AuditLevel::Warning => warn!("AUDIT[{}]: {}", event.event_type, event.description),
            AuditLevel::Info => info!("AUDIT[{}]: {}", event.event_type, event.description),
            AuditLevel::Debug => debug!("AUDIT[{}]: {}", event.event_type, event.description),
        }
    }

    /// 异步记录到文件
    async fn log_to_file_async(&mut self, event: &AuditEvent, log_path: &str) -> Result<()> {
        let log_entry = self.format_log_entry(event)?;
        
        // 检查是否需要日志轮转
        if self.should_rotate_log(&log_entry, log_path).await? {
            self.rotate_log_files(log_path).await?;
            self.current_log_size = 0;
        }

        // 追加到日志文件
        let mut file = fs::OpenOptions::new()
            .create(true)
            .append(true)
            .open(log_path)
            .await
            .map_err(|e| NativeMessagingError::ConfigError(format!("打开日志文件失败: {}", e)))?;

        file.write_all(log_entry.as_bytes()).await
            .map_err(|e| NativeMessagingError::ConfigError(format!("写入日志失败: {}", e)))?;

        file.flush().await
            .map_err(|e| NativeMessagingError::ConfigError(format!("刷新日志失败: {}", e)))?;

        self.current_log_size += log_entry.len();
        Ok(())
    }

    /// 同步记录到文件
    async fn log_to_file_sync(&mut self, event: &AuditEvent, log_path: &str) -> Result<()> {
        let log_entry = self.format_log_entry(event)?;
        let log_entry_len = log_entry.as_bytes().len();

        // 检查是否需要日志轮转
        if self.should_rotate_log(&log_entry, log_path).await? {
            self.rotate_log_files(log_path).await?;
            self.current_log_size = 0;
        }

        // 直接写入文件
        fs::write(log_path, log_entry).await
            .map_err(|e| NativeMessagingError::ConfigError(format!("写入日志文件失败: {}", e)))?;

        self.current_log_size += log_entry_len;
        Ok(())
    }

    /// 检查是否应该轮转日志
    async fn should_rotate_log(&self, new_entry: &str, log_path: &str) -> Result<bool> {
        if !PathBuf::from(log_path).exists() {
            return Ok(false);
        }

        let current_size = fs::metadata(log_path).await
            .map_err(|e| NativeMessagingError::ConfigError(format!("获取日志文件大小失败: {}", e)))?
            .len() as usize;

        Ok(current_size + new_entry.len() > self.config.max_log_file_size)
    }

    /// 轮转日志文件
    async fn rotate_log_files(&self, log_path: &str) -> Result<()> {
        let base_path = PathBuf::from(log_path);
        let base_name = base_path.file_stem()
            .and_then(|s| s.to_str())
            .unwrap_or("audit");
        let extension = base_path.extension()
            .and_then(|s| s.to_str())
            .unwrap_or("log");
        let parent_dir = base_path.parent().unwrap_or_else(|| std::path::Path::new("."));

        // 移动现有的轮转文件
        for i in (1..self.config.log_rotation_count).rev() {
            let old_file = parent_dir.join(format!("{}.{}.{}", base_name, i, extension));
            let new_file = parent_dir.join(format!("{}.{}.{}", base_name, i + 1, extension));
            
            if old_file.exists() {
                fs::rename(&old_file, &new_file).await
                    .map_err(|e| NativeMessagingError::ConfigError(format!("轮转日志文件失败: {}", e)))?;
            }
        }

        // 移动当前日志文件
        let current_file = PathBuf::from(log_path);
        let rotated_file = parent_dir.join(format!("{}.1.{}", base_name, extension));
        
        if current_file.exists() {
            fs::rename(&current_file, &rotated_file).await
                .map_err(|e| NativeMessagingError::ConfigError(format!("轮转当前日志文件失败: {}", e)))?;
        }

        info!("日志文件已轮转: {}", log_path);
        Ok(())
    }

    /// 格式化日志条目
    fn format_log_entry(&self, event: &AuditEvent) -> Result<String> {
        let timestamp = event.timestamp
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        let metadata_str = serde_json::to_string(&event.metadata)
            .map_err(|e| NativeMessagingError::ValidationError(format!("序列化元数据失败: {}", e)))?;

        Ok(format!(
            "[{}] {} {:>7} {:>15} {:>20} {} {}\n",
            timestamp,
            event.event_id,
            format!("{:?}", event.level),
            event.event_type,
            event.message_id,
            event.description,
            metadata_str
        ))
    }

    /// 获取事件计数
    pub fn get_event_count(&self) -> u64 {
        self.event_count
    }

    /// 获取当前日志大小
    pub fn get_current_log_size(&self) -> usize {
        self.current_log_size
    }

    /// 重置统计信息
    pub fn reset_stats(&mut self) {
        self.event_count = 0;
        self.current_log_size = 0;
        info!("审计记录器统计信息已重置");
    }

    /// 获取配置信息
    pub fn get_config(&self) -> &AuditLoggerConfig {
        &self.config
    }

    /// 更新配置
    pub fn update_config(&mut self, config: AuditLoggerConfig) -> Result<()> {
        // 验证新配置
        if config.enable_audit_logging && config.log_file_path.is_some() {
            let log_path = PathBuf::from(config.log_file_path.as_ref().unwrap());
            if let Some(parent) = log_path.parent() {
                if !parent.exists() {
                    return Err(NativeMessagingError::ConfigError(
                        format!("日志目录不存在: {:?}", parent)
                    ));
                }
            }
        }

        self.config = config;
        info!("审计记录器配置已更新");
        Ok(())
    }
}

/// 审计事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditEvent {
    /// 事件ID
    pub event_id: String,
    /// 事件类型
    pub event_type: String,
    /// 消息ID
    pub message_id: String,
    /// 来源
    pub source: String,
    /// 级别
    pub level: AuditLevel,
    /// 描述
    pub description: String,
    /// 元数据
    pub metadata: HashMap<String, String>,
    /// 时间戳
    pub timestamp: SystemTime,
}

impl AuditEvent {
    /// 创建新的审计事件
    pub fn new(
        event_type: String,
        message_id: String,
        source: String,
        level: AuditLevel,
        description: String,
    ) -> Self {
        Self {
            event_id: uuid::Uuid::new_v4().to_string(),
            event_type,
            message_id,
            source,
            level,
            description,
            metadata: HashMap::new(),
            timestamp: SystemTime::now(),
        }
    }

    /// 添加元数据
    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }

    /// 批量添加元数据
    pub fn with_metadata_map(mut self, metadata: HashMap<String, String>) -> Self {
        self.metadata.extend(metadata);
        self
    }

    /// 设置时间戳
    pub fn with_timestamp(mut self, timestamp: SystemTime) -> Self {
        self.timestamp = timestamp;
        self
    }
}

/// 审计级别
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum AuditLevel {
    /// 调试
    Debug,
    /// 信息
    Info,
    /// 警告
    Warning,
    /// 错误
    Error,
}

impl std::fmt::Display for AuditLevel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AuditLevel::Debug => write!(f, "DEBUG"),
            AuditLevel::Info => write!(f, "INFO"),
            AuditLevel::Warning => write!(f, "WARNING"),
            AuditLevel::Error => write!(f, "ERROR"),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[tokio::test]
    async fn test_audit_logger_creation() {
        let config = AuditLoggerConfig::default();
        let logger = AuditLogger::new(&config).unwrap();
        
        assert_eq!(logger.event_count, 0);
        assert!(logger.config.enable_audit_logging);
    }

    #[tokio::test]
    async fn test_audit_event_creation() {
        let event = AuditEvent::new(
            "test_event".to_string(),
            "msg-123".to_string(),
            "test-source".to_string(),
            AuditLevel::Info,
            "测试事件".to_string(),
        );

        assert_eq!(event.event_type, "test_event");
        assert_eq!(event.message_id, "msg-123");
        assert_eq!(event.level, AuditLevel::Info);
        assert!(!event.event_id.is_empty());
    }

    #[tokio::test]
    async fn test_audit_event_with_metadata() {
        let mut metadata = HashMap::new();
        metadata.insert("key1".to_string(), "value1".to_string());
        metadata.insert("key2".to_string(), "value2".to_string());

        let event = AuditEvent::new(
            "test_event".to_string(),
            "msg-123".to_string(),
            "test-source".to_string(),
            AuditLevel::Info,
            "测试事件".to_string(),
        )
        .with_metadata("single_key".to_string(), "single_value".to_string())
        .with_metadata_map(metadata);

        assert_eq!(event.metadata.len(), 3);
        assert_eq!(event.metadata.get("single_key"), Some(&"single_value".to_string()));
        assert_eq!(event.metadata.get("key1"), Some(&"value1".to_string()));
    }

    #[tokio::test]
    async fn test_log_level_filtering() {
        let mut config = AuditLoggerConfig::default();
        config.log_level = AuditLevel::Warning;
        config.log_file_path = None; // 只记录到控制台
        
        let mut logger = AuditLogger::new(&config).unwrap();

        let debug_event = AuditEvent::new(
            "debug_event".to_string(),
            "msg-1".to_string(),
            "test".to_string(),
            AuditLevel::Debug,
            "调试事件".to_string(),
        );

        let warning_event = AuditEvent::new(
            "warning_event".to_string(),
            "msg-2".to_string(),
            "test".to_string(),
            AuditLevel::Warning,
            "警告事件".to_string(),
        );

        // 调试事件应该被过滤掉
        logger.log_event(&debug_event).await.unwrap();
        assert_eq!(logger.event_count, 0);

        // 警告事件应该被记录
        logger.log_event(&warning_event).await.unwrap();
        assert_eq!(logger.event_count, 1);
    }

    #[tokio::test]
    async fn test_disabled_logging() {
        let mut config = AuditLoggerConfig::default();
        config.enable_audit_logging = false;
        
        let mut logger = AuditLogger::new(&config).unwrap();
        
        let event = AuditEvent::new(
            "test_event".to_string(),
            "msg-123".to_string(),
            "test".to_string(),
            AuditLevel::Info,
            "测试事件".to_string(),
        );

        logger.log_event(&event).await.unwrap();
        assert_eq!(logger.event_count, 0);
    }

    #[tokio::test]
    async fn test_batch_logging() {
        let mut config = AuditLoggerConfig::default();
        config.log_file_path = None; // 只记录到控制台
        
        let mut logger = AuditLogger::new(&config).unwrap();

        let events = vec![
            AuditEvent::new(
                "event1".to_string(),
                "msg-1".to_string(),
                "test".to_string(),
                AuditLevel::Info,
                "事件1".to_string(),
            ),
            AuditEvent::new(
                "event2".to_string(),
                "msg-2".to_string(),
                "test".to_string(),
                AuditLevel::Warning,
                "事件2".to_string(),
            ),
        ];

        logger.log_events(&events).await.unwrap();
        assert_eq!(logger.event_count, 2);
    }

    #[tokio::test]
    async fn test_config_update() {
        let config = AuditLoggerConfig::default();
        let mut logger = AuditLogger::new(&config).unwrap();

        let mut new_config = config.clone();
        new_config.log_level = AuditLevel::Error;
        new_config.max_log_file_size = 5 * 1024 * 1024; // 5MB

        logger.update_config(new_config).unwrap();
        assert_eq!(logger.config.log_level, AuditLevel::Error);
        assert_eq!(logger.config.max_log_file_size, 5 * 1024 * 1024);
    }

    #[tokio::test]
    async fn test_stats_reset() {
        let mut config = AuditLoggerConfig::default();
        config.log_file_path = None;
        
        let mut logger = AuditLogger::new(&config).unwrap();

        let event = AuditEvent::new(
            "test_event".to_string(),
            "msg-123".to_string(),
            "test".to_string(),
            AuditLevel::Info,
            "测试事件".to_string(),
        );

        logger.log_event(&event).await.unwrap();
        assert_eq!(logger.event_count, 1);

        logger.reset_stats();
        assert_eq!(logger.event_count, 0);
        assert_eq!(logger.current_log_size, 0);
    }
}