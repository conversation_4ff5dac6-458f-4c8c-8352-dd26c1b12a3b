//! 合规检查器模块
//!
//! 提供多种合规标准检查，包括 GDPR、SOX、HIPAA、PCI DSS 等

use crate::native_messaging::error::{NativeMessagingError, Result};
use crate::native_messaging::protocol::message::NativeMessage;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::SystemTime;
use tracing::{debug, info, warn, error};

/// 合规检查器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceCheckerConfig {
    /// 启用合规检查
    pub enable_compliance_checking: bool,
    /// 支持的合规标准
    pub supported_standards: Vec<ComplianceStandard>,
    /// 严格模式
    pub strict_mode: bool,
    /// 自定义检查规则
    pub custom_rules: HashMap<String, CustomComplianceRule>,
    /// 检查超时时间（毫秒）
    pub check_timeout_ms: u64,
}

impl Default for ComplianceCheckerConfig {
    fn default() -> Self {
        Self {
            enable_compliance_checking: true,
            supported_standards: vec![
                ComplianceStandard::GDPR,
                ComplianceStandard::SOX,
                ComplianceStandard::HIPAA,
                ComplianceStandard::PCI_DSS,
            ],
            strict_mode: false,
            custom_rules: HashMap::new(),
            check_timeout_ms: 5000, // 5秒超时
        }
    }
}

/// 合规检查器
#[derive(Debug)]
pub struct ComplianceChecker {
    config: ComplianceCheckerConfig,
    check_count: u64,
    violation_count: u64,
    standard_cache: HashMap<String, Vec<ComplianceRule>>,
}

impl ComplianceChecker {
    /// 创建新的合规检查器
    pub fn new(config: &ComplianceCheckerConfig) -> Result<Self> {
        let mut checker = Self {
            config: config.clone(),
            check_count: 0,
            violation_count: 0,
            standard_cache: HashMap::new(),
        };

        // 初始化标准缓存
        checker.initialize_standard_cache()?;
        
        Ok(checker)
    }

    /// 检查合规性
    pub async fn check_compliance(&mut self, message: &NativeMessage) -> Result<Vec<ComplianceResult>> {
        if !self.config.enable_compliance_checking {
            debug!("合规检查已禁用");
            return Ok(vec![]);
        }

        let mut results = Vec::new();
        let start_time = std::time::Instant::now();

        for standard in &self.config.supported_standards {
            // 检查超时
            if start_time.elapsed().as_millis() > self.config.check_timeout_ms as u128 {
                warn!("合规检查超时，跳过剩余标准");
                break;
            }

            match self.check_standard_compliance(standard, message).await {
                Ok(result) => {
                    if !result.is_compliant {
                        self.violation_count += 1;
                    }
                    results.push(result);
                }
                Err(e) => {
                    error!("检查标准 {:?} 时出错: {}", standard, e);
                    results.push(ComplianceResult {
                        standard: standard.clone(),
                        is_compliant: false,
                        violation_details: vec![format!("检查失败: {}", e)],
                        checked_at: SystemTime::now(),
                        recommendations: vec!["请检查系统配置".to_string()],
                        confidence_score: 0.0,
                        evidence: HashMap::new(),
                    });
                }
            }
        }

        self.check_count += 1;
        debug!("合规检查完成: {} 个标准，用时 {:?}", 
            results.len(), start_time.elapsed());
        
        Ok(results)
    }

    /// 检查特定标准的合规性
    async fn check_standard_compliance(&self, standard: &ComplianceStandard, message: &NativeMessage) -> Result<ComplianceResult> {
        let mut violations = Vec::new();
        let mut is_compliant = true;
        let mut evidence = HashMap::new();
        let mut confidence_score = 1.0;

        match standard {
            ComplianceStandard::GDPR => {
                self.check_gdpr_compliance(message, &mut violations, &mut is_compliant, &mut evidence, &mut confidence_score).await?;
            }
            ComplianceStandard::SOX => {
                self.check_sox_compliance(message, &mut violations, &mut is_compliant, &mut evidence, &mut confidence_score).await?;
            }
            ComplianceStandard::HIPAA => {
                self.check_hipaa_compliance(message, &mut violations, &mut is_compliant, &mut evidence, &mut confidence_score).await?;
            }
            ComplianceStandard::PCI_DSS => {
                self.check_pci_dss_compliance(message, &mut violations, &mut is_compliant, &mut evidence, &mut confidence_score).await?;
            }
            ComplianceStandard::Custom(name) => {
                self.check_custom_compliance(name, message, &mut violations, &mut is_compliant, &mut evidence, &mut confidence_score).await?;
            }
        }

        let recommendations = self.generate_recommendations(standard, &violations);
        
        Ok(ComplianceResult {
            standard: standard.clone(),
            is_compliant,
            violation_details: violations,
            checked_at: SystemTime::now(),
            recommendations,
            confidence_score,
            evidence,
        })
    }

    /// GDPR 合规检查
    async fn check_gdpr_compliance(
        &self,
        message: &NativeMessage,
        violations: &mut Vec<String>,
        is_compliant: &mut bool,
        evidence: &mut HashMap<String, String>,
        confidence_score: &mut f64,
    ) -> Result<()> {
        // 检查个人数据处理
        if self.contains_personal_data(message) {
            evidence.insert("personal_data_detected".to_string(), "true".to_string());
            
            if !self.has_encryption_marker(message) {
                violations.push("检测到个人数据但未加密".to_string());
                *is_compliant = false;
                *confidence_score *= 0.7;
            }

            if !self.has_consent_tracking(message) {
                violations.push("缺少同意跟踪信息".to_string());
                *is_compliant = false;
                *confidence_score *= 0.8;
            }

            if !self.has_purpose_limitation(message) {
                violations.push("缺少数据处理目的限制".to_string());
                *is_compliant = false;
                *confidence_score *= 0.9;
            }
        }

        // 检查数据最小化原则
        if self.check_data_minimization(message) {
            evidence.insert("data_minimization_check".to_string(), "passed".to_string());
        } else {
            violations.push("违反数据最小化原则".to_string());
            *is_compliant = false;
            *confidence_score *= 0.8;
        }

        Ok(())
    }

    /// SOX 合规检查
    async fn check_sox_compliance(
        &self,
        message: &NativeMessage,
        violations: &mut Vec<String>,
        is_compliant: &mut bool,
        evidence: &mut HashMap<String, String>,
        confidence_score: &mut f64,
    ) -> Result<()> {
        // 检查审计跟踪
        if !self.has_audit_trail(message) {
            violations.push("缺少审计跟踪信息".to_string());
            *is_compliant = false;
            *confidence_score *= 0.6;
        } else {
            evidence.insert("audit_trail".to_string(), "present".to_string());
        }

        // 检查数据完整性
        if !self.has_data_integrity_controls(message) {
            violations.push("数据完整性验证失败".to_string());
            *is_compliant = false;
            *confidence_score *= 0.7;
        } else {
            evidence.insert("data_integrity".to_string(), "verified".to_string());
        }

        // 检查访问控制
        if !self.has_proper_access_controls(message) {
            violations.push("访问控制不足".to_string());
            *is_compliant = false;
            *confidence_score *= 0.8;
        }

        Ok(())
    }

    /// HIPAA 合规检查
    async fn check_hipaa_compliance(
        &self,
        message: &NativeMessage,
        violations: &mut Vec<String>,
        is_compliant: &mut bool,
        evidence: &mut HashMap<String, String>,
        confidence_score: &mut f64,
    ) -> Result<()> {
        // 检查健康数据
        if self.contains_health_data(message) {
            evidence.insert("health_data_detected".to_string(), "true".to_string());
            
            if !self.has_encryption_marker(message) {
                violations.push("健康数据未加密".to_string());
                *is_compliant = false;
                *confidence_score *= 0.5; // HIPAA 对加密要求很严格
            }

            if !self.has_minimum_necessary_standard(message) {
                violations.push("违反最小必要标准".to_string());
                *is_compliant = false;
                *confidence_score *= 0.8;
            }
        }

        // 检查访问控制
        if !self.has_proper_access_controls(message) {
            violations.push("访问控制不足".to_string());
            *is_compliant = false;
            *confidence_score *= 0.7;
        }

        Ok(())
    }

    /// PCI DSS 合规检查
    async fn check_pci_dss_compliance(
        &self,
        message: &NativeMessage,
        violations: &mut Vec<String>,
        is_compliant: &mut bool,
        evidence: &mut HashMap<String, String>,
        confidence_score: &mut f64,
    ) -> Result<()> {
        // 检查支付数据
        if self.contains_payment_data(message) {
            evidence.insert("payment_data_detected".to_string(), "true".to_string());
            
            if !self.has_tokenization(message) && !self.has_encryption_marker(message) {
                violations.push("支付数据未标记化或加密".to_string());
                *is_compliant = false;
                *confidence_score *= 0.4; // PCI DSS 对支付数据要求极严
            }

            if !self.has_secure_transmission(message) {
                violations.push("传输安全性不足".to_string());
                *is_compliant = false;
                *confidence_score *= 0.6;
            }
        }

        // 检查网络安全
        if !self.has_network_security_controls(message) {
            violations.push("网络安全控制不足".to_string());
            *is_compliant = false;
            *confidence_score *= 0.8;
        }

        Ok(())
    }

    /// 自定义合规检查
    async fn check_custom_compliance(
        &self,
        standard_name: &str,
        message: &NativeMessage,
        violations: &mut Vec<String>,
        is_compliant: &mut bool,
        evidence: &mut HashMap<String, String>,
        confidence_score: &mut f64,
    ) -> Result<()> {
        if let Some(custom_rule) = self.config.custom_rules.get(standard_name) {
            // 执行自定义规则检查
            let rule_result = self.evaluate_custom_rule(custom_rule, message).await?;
            
            if !rule_result.passed {
                violations.push(format!("自定义规则违规: {}", rule_result.description));
                *is_compliant = false;
                *confidence_score *= rule_result.confidence_impact;
            }
            
            evidence.insert("custom_rule_executed".to_string(), custom_rule.name.clone());
        } else {
            violations.push(format!("自定义标准 {} 检查未实现", standard_name));
            *is_compliant = false;
            *confidence_score *= 0.5;
        }

        Ok(())
    }

    /// 评估自定义规则
    async fn evaluate_custom_rule(&self, rule: &CustomComplianceRule, message: &NativeMessage) -> Result<CustomRuleResult> {
        // 简化实现 - 实际应该支持复杂的规则表达式
        let payload_str = serde_json::to_string(&message.payload)
            .map_err(|e| NativeMessagingError::ValidationError(e.to_string()))?;

        let passed = match &rule.rule_type {
            CustomRuleType::Contains(keywords) => {
                keywords.iter().any(|keyword| payload_str.contains(keyword))
            }
            CustomRuleType::NotContains(keywords) => {
                !keywords.iter().any(|keyword| payload_str.contains(keyword))
            }
            CustomRuleType::SizeLimit(max_size) => {
                payload_str.len() <= *max_size
            }
            CustomRuleType::RequiredField(field) => {
                message.payload.get(field).is_some()
            }
        };

        Ok(CustomRuleResult {
            passed,
            description: rule.description.clone(),
            confidence_impact: if passed { 1.0 } else { rule.severity_factor },
        })
    }

    // 辅助检查方法
    fn contains_personal_data(&self, message: &NativeMessage) -> bool {
        let payload_str = serde_json::to_string(&message.payload).unwrap_or_default();
        let personal_data_indicators = [
            "email", "phone", "ssn", "name", "address", "birth", "passport", "license"
        ];
        personal_data_indicators.iter().any(|indicator| payload_str.to_lowercase().contains(indicator))
    }

    fn contains_health_data(&self, message: &NativeMessage) -> bool {
        let payload_str = serde_json::to_string(&message.payload).unwrap_or_default();
        let health_indicators = [
            "medical", "health", "diagnosis", "treatment", "patient", "doctor", "hospital", "medication"
        ];
        health_indicators.iter().any(|indicator| payload_str.to_lowercase().contains(indicator))
    }

    fn contains_payment_data(&self, message: &NativeMessage) -> bool {
        let payload_str = serde_json::to_string(&message.payload).unwrap_or_default();
        let payment_indicators = [
            "card", "payment", "transaction", "amount", "cvv", "expiry", "account_number", "routing"
        ];
        payment_indicators.iter().any(|indicator| payload_str.to_lowercase().contains(indicator))
    }

    fn has_encryption_marker(&self, message: &NativeMessage) -> bool {
        message.payload.get("encrypted").is_some() ||
        message.payload.get("cipher").is_some() ||
        message.payload.get("encrypted_data").is_some()
    }

    fn has_consent_tracking(&self, message: &NativeMessage) -> bool {
        message.payload.get("consent_id").is_some() ||
        message.payload.get("user_consent").is_some() ||
        message.payload.get("consent_timestamp").is_some()
    }

    fn has_purpose_limitation(&self, message: &NativeMessage) -> bool {
        message.payload.get("processing_purpose").is_some() ||
        message.payload.get("data_purpose").is_some()
    }

    fn check_data_minimization(&self, _message: &NativeMessage) -> bool {
        // 简化实现 - 实际应该检查数据字段是否必要
        true
    }

    fn has_audit_trail(&self, message: &NativeMessage) -> bool {
        message.payload.get("audit_id").is_some() ||
        message.payload.get("transaction_id").is_some() ||
        !message.request_id.is_empty()
    }

    fn has_data_integrity_controls(&self, message: &NativeMessage) -> bool {
        message.payload.get("checksum").is_some() ||
        message.payload.get("hash").is_some() ||
        message.payload.get("signature").is_some()
    }

    fn has_proper_access_controls(&self, message: &NativeMessage) -> bool {
        message.payload.get("access_token").is_some() ||
        message.payload.get("authorization").is_some() ||
        message.payload.get("user_id").is_some()
    }

    fn has_minimum_necessary_standard(&self, _message: &NativeMessage) -> bool {
        // 简化实现 - 实际应该检查数据访问是否符合最小必要原则
        true
    }

    fn has_tokenization(&self, message: &NativeMessage) -> bool {
        message.payload.get("token").is_some() ||
        message.payload.get("tokenized").is_some() ||
        message.payload.get("card_token").is_some()
    }

    fn has_secure_transmission(&self, _message: &NativeMessage) -> bool {
        // 简化实现 - 实际应该检查传输层安全
        true
    }

    fn has_network_security_controls(&self, _message: &NativeMessage) -> bool {
        // 简化实现 - 实际应该检查网络安全控制
        true
    }

    /// 生成建议
    fn generate_recommendations(&self, standard: &ComplianceStandard, violations: &[String]) -> Vec<String> {
        if violations.is_empty() {
            return vec!["合规检查通过".to_string()];
        }

        let mut recommendations = Vec::new();

        match standard {
            ComplianceStandard::GDPR => {
                recommendations.extend(vec![
                    "实施端到端数据加密".to_string(),
                    "添加用户同意跟踪机制".to_string(),
                    "实施数据最小化原则".to_string(),
                    "建立数据处理目的记录".to_string(),
                    "实施数据主体权利管理".to_string(),
                ]);
            }
            ComplianceStandard::SOX => {
                recommendations.extend(vec![
                    "增强审计跟踪功能".to_string(),
                    "实施数据完整性检查".to_string(),
                    "加强访问控制机制".to_string(),
                    "建立变更管理流程".to_string(),
                    "实施职责分离控制".to_string(),
                ]);
            }
            ComplianceStandard::HIPAA => {
                recommendations.extend(vec![
                    "加密所有健康数据".to_string(),
                    "实施基于角色的访问控制".to_string(),
                    "添加详细的审计日志".to_string(),
                    "实施最小必要访问原则".to_string(),
                    "建立数据泄露响应计划".to_string(),
                ]);
            }
            ComplianceStandard::PCI_DSS => {
                recommendations.extend(vec![
                    "实施支付数据标记化".to_string(),
                    "加强传输层安全".to_string(),
                    "定期进行安全评估".to_string(),
                    "实施网络分段".to_string(),
                    "建立入侵检测系统".to_string(),
                ]);
            }
            ComplianceStandard::Custom(name) => {
                recommendations.push(format!("联系管理员获取 {} 合规要求详情", name));
                recommendations.push("审查自定义合规规则配置".to_string());
            }
        }

        recommendations
    }

    /// 初始化标准缓存
    fn initialize_standard_cache(&mut self) -> Result<()> {
        // 预加载合规规则到缓存中
        for standard in &self.config.supported_standards {
            let rules = self.load_rules_for_standard(standard)?;
            self.standard_cache.insert(format!("{:?}", standard), rules);
        }
        Ok(())
    }

    /// 加载标准规则
    fn load_rules_for_standard(&self, standard: &ComplianceStandard) -> Result<Vec<ComplianceRule>> {
        // 简化实现 - 实际应该从配置文件或数据库加载
        match standard {
            ComplianceStandard::GDPR => Ok(vec![
                ComplianceRule {
                    id: "gdpr_001".to_string(),
                    name: "数据加密".to_string(),
                    description: "个人数据必须加密".to_string(),
                    severity: RuleSeverity::High,
                },
                ComplianceRule {
                    id: "gdpr_002".to_string(),
                    name: "同意跟踪".to_string(),
                    description: "必须跟踪用户同意".to_string(),
                    severity: RuleSeverity::Medium,
                },
            ]),
            _ => Ok(vec![]),
        }
    }

    /// 获取检查统计信息
    pub fn get_stats(&self) -> ComplianceStats {
        ComplianceStats {
            total_checks: self.check_count,
            total_violations: self.violation_count,
            compliance_rate: if self.check_count > 0 {
                (self.check_count - self.violation_count) as f64 / self.check_count as f64
            } else {
                1.0
            },
            supported_standards_count: self.config.supported_standards.len() as u64,
        }
    }

    /// 重置统计信息
    pub fn reset_stats(&mut self) {
        self.check_count = 0;
        self.violation_count = 0;
        info!("合规检查器统计信息已重置");
    }

    /// 获取检查计数
    pub fn get_check_count(&self) -> u64 {
        self.check_count
    }

    /// 获取违规计数
    pub fn get_violation_count(&self) -> u64 {
        self.violation_count
    }

    /// 更新配置
    pub fn update_config(&mut self, config: ComplianceCheckerConfig) -> Result<()> {
        self.config = config;
        self.standard_cache.clear();
        self.initialize_standard_cache()?;
        info!("合规检查器配置已更新");
        Ok(())
    }
}

/// 合规标准
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ComplianceStandard {
    /// 通用数据保护条例
    GDPR,
    /// 萨班斯-奥克斯利法案
    SOX,
    /// 健康保险流通与责任法案
    HIPAA,
    /// 支付卡行业数据安全标准
    PCI_DSS,
    /// 自定义标准
    Custom(String),
}

/// 合规检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceResult {
    /// 合规标准
    pub standard: ComplianceStandard,
    /// 是否合规
    pub is_compliant: bool,
    /// 违规详情
    pub violation_details: Vec<String>,
    /// 检查时间
    pub checked_at: SystemTime,
    /// 建议
    pub recommendations: Vec<String>,
    /// 置信度分数 (0.0 - 1.0)
    pub confidence_score: f64,
    /// 检查证据
    pub evidence: HashMap<String, String>,
}

/// 自定义合规规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomComplianceRule {
    /// 规则名称
    pub name: String,
    /// 规则描述
    pub description: String,
    /// 规则类型
    pub rule_type: CustomRuleType,
    /// 严重程度因子 (0.0 - 1.0)
    pub severity_factor: f64,
}

/// 自定义规则类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CustomRuleType {
    /// 包含关键词
    Contains(Vec<String>),
    /// 不包含关键词
    NotContains(Vec<String>),
    /// 大小限制
    SizeLimit(usize),
    /// 必需字段
    RequiredField(String),
}

/// 自定义规则结果
#[derive(Debug)]
struct CustomRuleResult {
    passed: bool,
    description: String,
    confidence_impact: f64,
}

/// 合规规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceRule {
    /// 规则ID
    pub id: String,
    /// 规则名称
    pub name: String,
    /// 规则描述
    pub description: String,
    /// 严重程度
    pub severity: RuleSeverity,
}

/// 规则严重程度
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RuleSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// 合规统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceStats {
    /// 总检查次数
    pub total_checks: u64,
    /// 总违规次数
    pub total_violations: u64,
    /// 合规率
    pub compliance_rate: f64,
    /// 支持的标准数量
    pub supported_standards_count: u64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    fn create_test_message() -> NativeMessage {
        NativeMessage {
            version: 1,
            request_id: "test-123".to_string(),
            message_type: crate::native_messaging::protocol::message::MessageType::Password,
            source: "test-extension".to_string(),
            payload: json!({"test": "data"}),
            timestamp: std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs(),
            metadata: std::collections::HashMap::new(),
            extensions: std::collections::HashMap::new(),
            priority: crate::native_messaging::protocol::message::MessagePriority::Normal,
            timeout_ms: None,
        }
    }

    fn create_test_message_with_personal_data() -> NativeMessage {
        NativeMessage {
            request_id: "test-456".to_string(),
            message_type: crate::native_messaging::protocol::message::MessageType::PasswordGenerate,
            source: "test-extension".to_string(),
            payload: json!({
                "email": "<EMAIL>",
                "name": "John Doe"
            }),
            timestamp: std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs(),
            version: 1,
            metadata: std::collections::HashMap::new(),
            extensions: std::collections::HashMap::new(),
            priority: crate::native_messaging::protocol::message::MessagePriority::Normal,
            timeout_ms: None,
        }
    }

    #[tokio::test]
    async fn test_compliance_checker_creation() {
        let config = ComplianceCheckerConfig::default();
        let checker = ComplianceChecker::new(&config).unwrap();
        
        assert_eq!(checker.check_count, 0);
        assert_eq!(checker.violation_count, 0);
        assert!(checker.config.enable_compliance_checking);
    }

    #[tokio::test]
    async fn test_basic_compliance_check() {
        let config = ComplianceCheckerConfig::default();
        let mut checker = ComplianceChecker::new(&config).unwrap();
        let message = create_test_message();
        
        let results = checker.check_compliance(&message).await.unwrap();
        assert_eq!(results.len(), 4); // GDPR, SOX, HIPAA, PCI_DSS
        assert_eq!(checker.check_count, 1);
    }

    #[tokio::test]
    async fn test_disabled_compliance_checking() {
        let mut config = ComplianceCheckerConfig::default();
        config.enable_compliance_checking = false;
        
        let mut checker = ComplianceChecker::new(&config).unwrap();
        let message = create_test_message();
        
        let results = checker.check_compliance(&message).await.unwrap();
        assert_eq!(results.len(), 0);
        assert_eq!(checker.check_count, 0);
    }

    #[tokio::test]
    async fn test_gdpr_personal_data_detection() {
        let config = ComplianceCheckerConfig::default();
        let checker = ComplianceChecker::new(&config).unwrap();
        
        let message_with_personal_data = create_test_message_with_personal_data();
        assert!(checker.contains_personal_data(&message_with_personal_data));
        
        let normal_message = create_test_message();
        assert!(!checker.contains_personal_data(&normal_message));
    }

    #[tokio::test]
    async fn test_health_data_detection() {
        let config = ComplianceCheckerConfig::default();
        let checker = ComplianceChecker::new(&config).unwrap();
        
        let message = NativeMessage {
            request_id: "test-789".to_string(),
            message_type: crate::native_messaging::protocol::message::MessageType::PasswordGenerate,
            source: "test-extension".to_string(),
            payload: json!({
                "medical_record": "patient data",
                "diagnosis": "test condition"
            }),
            timestamp: std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs(),
            version: 1,
            metadata: std::collections::HashMap::new(),
            extensions: std::collections::HashMap::new(),
            priority: crate::native_messaging::protocol::message::MessagePriority::Normal,
            timeout_ms: None,
        };
        
        assert!(checker.contains_health_data(&message));
    }

    #[tokio::test]
    async fn test_payment_data_detection() {
        let config = ComplianceCheckerConfig::default();
        let checker = ComplianceChecker::new(&config).unwrap();
        
        let message = NativeMessage {
            request_id: "test-payment".to_string(),
            message_type: crate::native_messaging::protocol::message::MessageType::PasswordGenerate,
            source: "test-extension".to_string(),
            payload: json!({
                "card_number": "1234-5678-9012-3456",
                "amount": "100.00"
            }),
            timestamp: std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs(),
            version: 1,
            metadata: std::collections::HashMap::new(),
            extensions: std::collections::HashMap::new(),
            priority: crate::native_messaging::protocol::message::MessagePriority::Normal,
            timeout_ms: None,
        };
        
        assert!(checker.contains_payment_data(&message));
    }

    #[tokio::test]
    async fn test_encryption_marker_detection() {
        let config = ComplianceCheckerConfig::default();
        let checker = ComplianceChecker::new(&config).unwrap();
        
        let encrypted_message = NativeMessage {
            request_id: "test-encrypted".to_string(),
            message_type: crate::native_messaging::protocol::message::MessageType::PasswordGenerate,
            source: "test-extension".to_string(),
            payload: json!({
                "data": "some data",
                "encrypted": true
            }),
            timestamp: std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs(),
            version: 1,
            metadata: std::collections::HashMap::new(),
            extensions: std::collections::HashMap::new(),
            priority: crate::native_messaging::protocol::message::MessagePriority::Normal,
            timeout_ms: None,
        };
        
        assert!(checker.has_encryption_marker(&encrypted_message));
        
        let normal_message = create_test_message();
        assert!(!checker.has_encryption_marker(&normal_message));
    }

    #[tokio::test]
    async fn test_custom_compliance_rule() {
        let mut config = ComplianceCheckerConfig::default();
        config.custom_rules.insert(
            "test_rule".to_string(),
            CustomComplianceRule {
                name: "Test Rule".to_string(),
                description: "测试规则".to_string(),
                rule_type: CustomRuleType::RequiredField("required_field".to_string()),
                severity_factor: 0.8,
            }
        );
        config.supported_standards = vec![ComplianceStandard::Custom("test_rule".to_string())];
        
        let mut checker = ComplianceChecker::new(&config).unwrap();
        
        let message_without_field = create_test_message();
        let results = checker.check_compliance(&message_without_field).await.unwrap();
        assert_eq!(results.len(), 1);
        assert!(!results[0].is_compliant);
    }

    #[tokio::test]
    async fn test_confidence_score_calculation() {
        let config = ComplianceCheckerConfig::default();
        let mut checker = ComplianceChecker::new(&config).unwrap();
        
        // 测试带有个人数据但未加密的消息
        let message = NativeMessage {
            request_id: "test-confidence".to_string(),
            message_type: crate::native_messaging::protocol::message::MessageType::PasswordGenerate,
            source: "test-extension".to_string(),
            payload: json!({
                "email": "<EMAIL>",
                "name": "John Doe"
                // 没有加密标记
            }),
            timestamp: std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs(),
            version: 1,
            metadata: std::collections::HashMap::new(),
            extensions: std::collections::HashMap::new(),
            priority: crate::native_messaging::protocol::message::MessagePriority::Normal,
            timeout_ms: None,
        };
        
        let results = checker.check_compliance(&message).await.unwrap();
        let gdpr_result = results.iter().find(|r| matches!(r.standard, ComplianceStandard::GDPR)).unwrap();
        
        // 置信度应该因为违规而降低
        assert!(gdpr_result.confidence_score < 1.0);
        assert!(!gdpr_result.is_compliant);
    }

    #[tokio::test]
    async fn test_stats_tracking() {
        let config = ComplianceCheckerConfig::default();
        let mut checker = ComplianceChecker::new(&config).unwrap();
        
        let message1 = create_test_message();
        let message2 = create_test_message_with_personal_data();
        
        checker.check_compliance(&message1).await.unwrap();
        checker.check_compliance(&message2).await.unwrap();
        
        let stats = checker.get_stats();
        assert_eq!(stats.total_checks, 2);
        assert!(stats.compliance_rate <= 1.0);
        assert_eq!(stats.supported_standards_count, 4);
    }

    #[tokio::test]
    async fn test_stats_reset() {
        let config = ComplianceCheckerConfig::default();
        let mut checker = ComplianceChecker::new(&config).unwrap();
        
        let message = create_test_message();
        checker.check_compliance(&message).await.unwrap();
        
        assert_eq!(checker.get_check_count(), 1);
        
        checker.reset_stats();
        assert_eq!(checker.get_check_count(), 0);
        assert_eq!(checker.get_violation_count(), 0);
    }
}
