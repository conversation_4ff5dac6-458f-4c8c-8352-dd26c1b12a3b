//! 策略引擎模块
//!
//! 提供策略规则评估、条件判断和策略动作执行功能

use crate::native_messaging::error::{NativeMessagingError, Result};
use crate::native_messaging::protocol::message::NativeMessage;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};
use tracing::{debug, info, warn, error};

/// 策略引擎配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PolicyEngineConfig {
    /// 启用策略执行
    pub enable_policy_execution: bool,
    /// 默认策略动作
    pub default_action: PolicyAction,
    /// 策略规则
    pub policy_rules: Vec<PolicyRule>,
}

impl Default for PolicyEngineConfig {
    fn default() -> Self {
        Self {
            enable_policy_execution: true,
            default_action: PolicyAction::Allow,
            policy_rules: Self::default_rules(),
        }
    }
}

impl PolicyEngineConfig {
    fn default_rules() -> Vec<PolicyRule> {
        vec![
            PolicyRule {
                id: "policy_001".to_string(),
                name: "Message Size Limit".to_string(),
                description: "限制消息大小".to_string(),
                condition: PolicyCondition::MessageSizeLimit(1024 * 1024), // 1MB
                action: PolicyAction::Block,
                enabled: true,
                priority: 100,
            },
            PolicyRule {
                id: "policy_002".to_string(),
                name: "Source Whitelist".to_string(),
                description: "来源白名单检查".to_string(),
                condition: PolicyCondition::SourceWhitelist(vec![
                    "trusted-extension-1".to_string(),
                    "trusted-extension-2".to_string(),
                ]),
                action: PolicyAction::Allow,
                enabled: true,
                priority: 90,
            },
            PolicyRule {
                id: "policy_003".to_string(),
                name: "Rate Limit".to_string(),
                description: "频率限制".to_string(),
                condition: PolicyCondition::RateLimit(100), // 每分钟100次
                action: PolicyAction::RateLimit,
                enabled: true,
                priority: 80,
            },
        ]
    }
}

/// 策略引擎
#[derive(Debug)]
pub struct PolicyEngine {
    config: PolicyEngineConfig,
    rule_cache: HashMap<String, PolicyRule>,
}

impl PolicyEngine {
    /// 创建新的策略引擎
    pub fn new(config: &PolicyEngineConfig) -> Result<Self> {
        let mut rule_cache = HashMap::new();
        for rule in &config.policy_rules {
            rule_cache.insert(rule.id.clone(), rule.clone());
        }

        Ok(Self {
            config: config.clone(),
            rule_cache,
        })
    }

    /// 评估策略
    pub async fn evaluate_policies(&self, message: &NativeMessage) -> Result<Vec<PolicyResult>> {
        if !self.config.enable_policy_execution {
            debug!("策略执行已禁用");
            return Ok(vec![]);
        }

        let mut results = Vec::new();

        for rule in &self.config.policy_rules {
            if !rule.enabled {
                continue;
            }

            let is_match = self.evaluate_condition(&rule.condition, message).await?;

            results.push(PolicyResult {
                rule_id: rule.id.clone(),
                rule_name: rule.name.clone(),
                is_allowed: match rule.action {
                    PolicyAction::Allow => true,
                    PolicyAction::Block => !is_match,
                    PolicyAction::Monitor => true,
                    PolicyAction::RateLimit => true, // 简化处理
                },
                action: rule.action.clone(),
                reason: if is_match {
                    format!("匹配规则: {}", rule.description)
                } else {
                    "未匹配规则".to_string()
                },
                evaluated_at: SystemTime::now(),
            });
        }

        debug!("策略评估完成，共 {} 条规则", results.len());
        Ok(results)
    }

    /// 评估条件
    async fn evaluate_condition(&self, condition: &PolicyCondition, message: &NativeMessage) -> Result<bool> {
        match condition {
            PolicyCondition::MessageSizeLimit(limit) => {
                let message_size = serde_json::to_string(&message.payload)
                    .map_err(|e| NativeMessagingError::ValidationError(e.to_string()))?
                    .len();
                Ok(message_size > *limit)
            }
            PolicyCondition::SourceWhitelist(whitelist) => {
                Ok(whitelist.contains(&message.source))
            }
            PolicyCondition::RateLimit(_limit) => {
                // 简化实现，实际应该检查频率
                // TODO: 实现真正的频率限制检查
                Ok(false)
            }
            PolicyCondition::Custom(expression) => {
                // 简化实现，实际应该解析表达式
                // TODO: 实现表达式解析器
                Ok(expression.contains(&message.source))
            }
        }
    }

    /// 获取规则缓存
    pub fn get_rule_cache(&self) -> &HashMap<String, PolicyRule> {
        &self.rule_cache
    }

    /// 更新规则
    pub fn update_rule(&mut self, rule: PolicyRule) -> Result<()> {
        let rule_id = rule.id.clone();
        self.rule_cache.insert(rule.id.clone(), rule.clone());
        
        // 更新配置中的规则
        if let Some(config_rule) = self.config.policy_rules.iter_mut()
            .find(|r| r.id == rule.id) {
            *config_rule = rule;
        } else {
            self.config.policy_rules.push(rule);
        }
        
        info!("策略规则已更新: {}", rule_id);
        Ok(())
    }

    /// 删除规则
    pub fn remove_rule(&mut self, rule_id: &str) -> Result<()> {
        self.rule_cache.remove(rule_id);
        self.config.policy_rules.retain(|r| r.id != rule_id);
        info!("策略规则已删除: {}", rule_id);
        Ok(())
    }
}

/// 策略规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PolicyRule {
    /// 规则ID
    pub id: String,
    /// 规则名称
    pub name: String,
    /// 规则描述
    pub description: String,
    /// 条件
    pub condition: PolicyCondition,
    /// 动作
    pub action: PolicyAction,
    /// 是否启用
    pub enabled: bool,
    /// 优先级
    pub priority: u32,
}

/// 策略条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PolicyCondition {
    /// 消息大小限制
    MessageSizeLimit(usize),
    /// 来源白名单
    SourceWhitelist(Vec<String>),
    /// 频率限制
    RateLimit(u32),
    /// 自定义表达式
    Custom(String),
}

/// 策略动作
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum PolicyAction {
    /// 允许
    Allow,
    /// 阻止
    Block,
    /// 监控
    Monitor,
    /// 限流
    RateLimit,
}

/// 策略结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PolicyResult {
    /// 规则ID
    pub rule_id: String,
    /// 规则名称
    pub rule_name: String,
    /// 是否允许
    pub is_allowed: bool,
    /// 动作
    pub action: PolicyAction,
    /// 原因
    pub reason: String,
    /// 评估时间
    pub evaluated_at: SystemTime,
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;
    use crate::native_messaging::protocol::message::MessagePriority;
    fn create_test_message() -> NativeMessage {
        let now = SystemTime::now();
        let timestamp = now.duration_since(UNIX_EPOCH).unwrap().as_secs(); // 转换为 u64 秒级时间戳

        NativeMessage {
            request_id: "test-123".to_string(),
            message_type: crate::native_messaging::protocol::message::MessageType::PasswordGenerate,
            source: "test-extension".to_string(),
            payload: json!({"test": "data"}),
            timestamp: timestamp,
            version: 1,
            metadata: HashMap::new(),
            extensions: HashMap::new(),
            priority: MessagePriority::Low,
            timeout_ms: Some(10000),
        }
    }

    #[tokio::test]
    async fn test_policy_engine_creation() {
        let config = PolicyEngineConfig::default();
        let engine = PolicyEngine::new(&config).unwrap();
        
        assert_eq!(engine.rule_cache.len(), 3);
        assert!(engine.config.enable_policy_execution);
    }

    #[tokio::test]
    async fn test_policy_evaluation() {
        let config = PolicyEngineConfig::default();
        let engine = PolicyEngine::new(&config).unwrap();
        let message = create_test_message();
        
        let results = engine.evaluate_policies(&message).await.unwrap();
        assert_eq!(results.len(), 3);
    }

    #[tokio::test]
    async fn test_message_size_limit() {
        let mut config = PolicyEngineConfig::default();
        config.policy_rules = vec![
            PolicyRule {
                id: "size_test".to_string(),
                name: "Size Test".to_string(),
                description: "测试大小限制".to_string(),
                condition: PolicyCondition::MessageSizeLimit(10), // 很小的限制
                action: PolicyAction::Block,
                enabled: true,
                priority: 100,
            }
        ];
        
        let engine = PolicyEngine::new(&config).unwrap();
        let message = create_test_message();
        
        let results = engine.evaluate_policies(&message).await.unwrap();
        assert_eq!(results.len(), 1);
        assert!(!results[0].is_allowed); // 应该被阻止
    }

    #[tokio::test]
    async fn test_source_whitelist() {
        let mut config = PolicyEngineConfig::default();
        config.policy_rules = vec![
            PolicyRule {
                id: "whitelist_test".to_string(),
                name: "Whitelist Test".to_string(),
                description: "测试白名单".to_string(),
                condition: PolicyCondition::SourceWhitelist(vec!["allowed-extension".to_string()]),
                action: PolicyAction::Allow,
                enabled: true,
                priority: 100,
            }
        ];
        
        let engine = PolicyEngine::new(&config).unwrap();
        let mut message = create_test_message();
        message.source = "allowed-extension".to_string();
        
        let results = engine.evaluate_policies(&message).await.unwrap();
        assert_eq!(results.len(), 1);
        assert!(results[0].is_allowed);
    }

    #[tokio::test]
    async fn test_disabled_policy_execution() {
        let mut config = PolicyEngineConfig::default();
        config.enable_policy_execution = false;
        
        let engine = PolicyEngine::new(&config).unwrap();
        let message = create_test_message();
        
        let results = engine.evaluate_policies(&message).await.unwrap();
        assert_eq!(results.len(), 0);
    }

    #[tokio::test]
    async fn test_rule_management() {
        let config = PolicyEngineConfig::default();
        let mut engine = PolicyEngine::new(&config).unwrap();
        
        let new_rule = PolicyRule {
            id: "new_rule".to_string(),
            name: "New Rule".to_string(),
            description: "新规则".to_string(),
            condition: PolicyCondition::Custom("test".to_string()),
            action: PolicyAction::Monitor,
            enabled: true,
            priority: 50,
        };
        
        // 添加规则
        engine.update_rule(new_rule.clone()).unwrap();
        assert!(engine.rule_cache.contains_key("new_rule"));
        
        // 删除规则
        engine.remove_rule("new_rule").unwrap();
        assert!(!engine.rule_cache.contains_key("new_rule"));
    }
}