//! 消息净化器
//! 
//! 负责检测和清理消息中的危险内容，包括：
//! - SQL注入攻击载荷
//! - XSS攻击脚本
//! - 命令注入尝试
//! - 恶意文件路径
//! - 敏感信息泄露

use super::{ProtocolGuardConfig, ThreatInfo, ThreatLevel, ThreatType, SecurityAction, LogLevel};
use crate::native_messaging::error::{NativeMessagingError, Result};
use crate::native_messaging::protocol::message::NativeMessage;
use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::OnceLock;
use std::time::{SystemTime, UNIX_EPOCH};
use tracing::{debug, warn};

/// 消息净化器
pub struct MessageSanitizer {
    /// 配置信息
    config: ProtocolGuardConfig,
    /// SQL注入检测规则
    sql_injection_patterns: Vec<Regex>,
    /// XSS攻击检测规则
    xss_patterns: Vec<Regex>,
    /// 命令注入检测规则
    command_injection_patterns: Vec<Regex>,
    /// 路径遍历检测规则
    path_traversal_patterns: Vec<Regex>,
    /// 敏感信息检测规则
    sensitive_data_patterns: Vec<Regex>,
    /// 自定义净化规则
    custom_rules: Vec<SanitizationRule>,
}

/// 净化结果
#[derive(Debug, Clone)]
pub struct SanitizationResult {
    /// 净化后的消息
    pub sanitized_message: NativeMessage,
    /// 是否被修改
    pub was_modified: bool,
    /// 检测到的威胁
    pub detected_threats: Vec<ThreatInfo>,
    /// 净化操作详情
    pub sanitization_details: Vec<SanitizationOperation>,
    /// 净化耗时（微秒）
    pub processing_time_micros: u64,
}

/// 净化操作详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SanitizationOperation {
    /// 操作类型
    pub operation_type: SanitizationType,
    /// 字段路径
    pub field_path: String,
    /// 原始值
    pub original_value: String,
    /// 净化后的值
    pub sanitized_value: String,
    /// 触发的规则
    pub triggered_rule: String,
    /// 威胁级别
    pub threat_level: ThreatLevel,
}

/// 净化类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SanitizationType {
    /// 移除SQL注入
    SqlInjectionRemoval,
    /// 移除XSS脚本
    XssRemoval,
    /// 移除命令注入
    CommandInjectionRemoval,
    /// 路径规范化
    PathNormalization,
    /// 敏感信息脱敏
    SensitiveDataMasking,
    /// HTML实体编码
    HtmlEntityEncoding,
    /// URL编码
    UrlEncoding,
    /// 字符过滤
    CharacterFiltering,
    /// 长度截断
    LengthTruncation,
    /// 自定义规则
    CustomRule(String),
}

/// 自定义净化规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SanitizationRule {
    /// 规则名称
    pub name: String,
    /// 匹配模式
    pub pattern: String,
    /// 替换内容
    pub replacement: String,
    /// 威胁级别
    pub threat_level: ThreatLevel,
    /// 是否启用
    pub enabled: bool,
}

// 预编译的正则表达式模式
static SQL_INJECTION_PATTERNS: OnceLock<Vec<Regex>> = OnceLock::new();
static XSS_PATTERNS: OnceLock<Vec<Regex>> = OnceLock::new();
static COMMAND_INJECTION_PATTERNS: OnceLock<Vec<Regex>> = OnceLock::new();
static PATH_TRAVERSAL_PATTERNS: OnceLock<Vec<Regex>> = OnceLock::new();
static SENSITIVE_DATA_PATTERNS: OnceLock<Vec<Regex>> = OnceLock::new();

impl MessageSanitizer {
    /// 创建新的消息净化器
    pub async fn new(config: ProtocolGuardConfig) -> Result<Self> {
        let sanitizer = Self {
            config,
            sql_injection_patterns: Self::get_sql_injection_patterns(),
            xss_patterns: Self::get_xss_patterns(),
            command_injection_patterns: Self::get_command_injection_patterns(),
            path_traversal_patterns: Self::get_path_traversal_patterns(),
            sensitive_data_patterns: Self::get_sensitive_data_patterns(),
            custom_rules: Vec::new(),
        };

        debug!("消息净化器初始化完成，模式数量: SQL={}, XSS={}, CMD={}, PATH={}, SENSITIVE={}", 
               sanitizer.sql_injection_patterns.len(),
               sanitizer.xss_patterns.len(),
               sanitizer.command_injection_patterns.len(),
               sanitizer.path_traversal_patterns.len(),
               sanitizer.sensitive_data_patterns.len());

        Ok(sanitizer)
    }

    /// 净化消息
    pub async fn sanitize_message(&self, message: &NativeMessage) -> Result<SanitizationResult> {
        let start_time = std::time::Instant::now();
        let mut sanitized_message = message.clone();
        let mut was_modified = false;
        let mut detected_threats = Vec::new();
        let mut sanitization_details = Vec::new();

        // 1. 净化消息载荷
        if let Ok(sanitized_payload) = self.sanitize_json_value(&message.payload, "payload").await {
            if sanitized_payload.value != message.payload {
                sanitized_message.payload = sanitized_payload.value;
                was_modified = true;
                detected_threats.extend(sanitized_payload.threats);
                sanitization_details.extend(sanitized_payload.operations);
            }
        }

        // 2. 净化消息ID和来源
        let sanitized_request_id = self.sanitize_string(&message.request_id, "request_id").await;
        if sanitized_request_id.value != message.request_id {
            sanitized_message.request_id = sanitized_request_id.value;
            was_modified = true;
            detected_threats.extend(sanitized_request_id.threats);
            sanitization_details.extend(sanitized_request_id.operations);
        }

        let sanitized_source = self.sanitize_string(&message.source, "source").await;
        if sanitized_source.value != message.source {
            sanitized_message.source = sanitized_source.value;
            was_modified = true;
            detected_threats.extend(sanitized_source.threats);
            sanitization_details.extend(sanitized_source.operations);
        }

        // 3. 净化元数据
        let mut sanitized_metadata = HashMap::new();
        for (key, value) in &message.metadata {
            let sanitized_key = self.sanitize_string(key, &format!("metadata.{}", key)).await;
            let sanitized_value = self.sanitize_string(value, &format!("metadata.{}", key)).await;
            
            if sanitized_key.value != *key || sanitized_value.value != *value {
                was_modified = true;
                detected_threats.extend(sanitized_key.threats);
                detected_threats.extend(sanitized_value.threats);
                sanitization_details.extend(sanitized_key.operations);
                sanitization_details.extend(sanitized_value.operations);
            }
            
            sanitized_metadata.insert(sanitized_key.value, sanitized_value.value);
        }
        sanitized_message.metadata = sanitized_metadata;

        // 4. 净化扩展数据
        let mut sanitized_extensions = HashMap::new();
        for (key, value) in &message.extensions {
            let sanitized_result = self.sanitize_json_value(value, &format!("extensions.{}", key)).await?;
            if sanitized_result.value != *value {
                was_modified = true;
                detected_threats.extend(sanitized_result.threats);
                sanitization_details.extend(sanitized_result.operations);
            }
            sanitized_extensions.insert(key.clone(), sanitized_result.value);
        }
        sanitized_message.extensions = sanitized_extensions;

        let processing_time = start_time.elapsed();

        Ok(SanitizationResult {
            sanitized_message,
            was_modified,
            detected_threats,
            sanitization_details,
            processing_time_micros: processing_time.as_micros() as u64,
        })
    }

    /// 净化JSON值
    async fn sanitize_json_value(&self, value: &serde_json::Value, field_path: &str) -> Result<JsonSanitizationResult> {
        let mut result = JsonSanitizationResult {
            value: value.clone(),
            threats: Vec::new(),
            operations: Vec::new(),
        };

        match value {
            serde_json::Value::String(s) => {
                let sanitized = self.sanitize_string(s, field_path).await;
                result.value = serde_json::Value::String(sanitized.value);
                result.threats = sanitized.threats;
                result.operations = sanitized.operations;
            }
            serde_json::Value::Object(obj) => {
                let mut sanitized_obj = serde_json::Map::new();
                for (key, val) in obj {
                    let sanitized_key = self.sanitize_string(key, &format!("{}.{}", field_path, key)).await;
                    let sanitized_val = Box::pin(self.sanitize_json_value(val, &format!("{}.{}", field_path, key))).await?;
                    
                    sanitized_obj.insert(sanitized_key.value, sanitized_val.value);
                    result.threats.extend(sanitized_key.threats);
                    result.threats.extend(sanitized_val.threats);
                    result.operations.extend(sanitized_key.operations);
                    result.operations.extend(sanitized_val.operations);
                }
                result.value = serde_json::Value::Object(sanitized_obj);
            }
            serde_json::Value::Array(arr) => {
                let mut sanitized_arr = Vec::new();
                for (index, val) in arr.iter().enumerate() {
                    let sanitized_val = Box::pin(self.sanitize_json_value(val, &format!("{}[{}]", field_path, index))).await?;
                    sanitized_arr.push(sanitized_val.value);
                    result.threats.extend(sanitized_val.threats);
                    result.operations.extend(sanitized_val.operations);
                }
                result.value = serde_json::Value::Array(sanitized_arr);
            }
            _ => {
                // 其他类型（Number, Bool, Null）不需要净化
            }
        }

        Ok(result)
    }

    /// 净化字符串
    async fn sanitize_string(&self, input: &str, field_path: &str) -> StringSanitizationResult {
        let mut sanitized = input.to_string();
        let mut threats = Vec::new();
        let mut operations = Vec::new();

        // 1. SQL注入检测和净化
        for pattern in &self.sql_injection_patterns {
            if pattern.is_match(&sanitized) {
                let original = sanitized.clone();
                sanitized = pattern.replace_all(&sanitized, "[REMOVED]").to_string();
                
                let threat = ThreatInfo {
                    source: field_path.to_string(),
                    level: ThreatLevel::High,
                    threat_type: ThreatType::SqlInjection,
                    detected_at: SystemTime::now(),
                    description: "检测到SQL注入尝试".to_string(),
                    evidence: vec![original.clone()],
                    recommended_actions: vec![SecurityAction::Log(LogLevel::Warn), SecurityAction::Sanitize],
                };
                threats.push(threat);

                operations.push(SanitizationOperation {
                    operation_type: SanitizationType::SqlInjectionRemoval,
                    field_path: field_path.to_string(),
                    original_value: original,
                    sanitized_value: sanitized.clone(),
                    triggered_rule: "SQL_INJECTION".to_string(),
                    threat_level: ThreatLevel::High,
                });

                warn!("检测到SQL注入尝试，字段: {}", field_path);
            }
        }

        // 2. XSS攻击检测和净化
        for pattern in &self.xss_patterns {
            if pattern.is_match(&sanitized) {
                let original = sanitized.clone();
                sanitized = pattern.replace_all(&sanitized, "[REMOVED]").to_string();
                
                let threat = ThreatInfo {
                    source: field_path.to_string(),
                    level: ThreatLevel::Medium,
                    threat_type: ThreatType::CrossSiteScripting,
                    detected_at: SystemTime::now(),
                    description: "检测到XSS攻击尝试".to_string(),
                    evidence: vec![original.clone()],
                    recommended_actions: vec![SecurityAction::Log(LogLevel::Warn), SecurityAction::Sanitize],
                };
                threats.push(threat);

                operations.push(SanitizationOperation {
                    operation_type: SanitizationType::XssRemoval,
                    field_path: field_path.to_string(),
                    original_value: original,
                    sanitized_value: sanitized.clone(),
                    triggered_rule: "XSS_ATTACK".to_string(),
                    threat_level: ThreatLevel::Medium,
                });

                warn!("检测到XSS攻击尝试，字段: {}", field_path);
            }
        }

        // 3. 命令注入检测和净化
        for pattern in &self.command_injection_patterns {
            if pattern.is_match(&sanitized) {
                let original = sanitized.clone();
                sanitized = pattern.replace_all(&sanitized, "[REMOVED]").to_string();
                
                let threat = ThreatInfo {
                    source: field_path.to_string(),
                    level: ThreatLevel::High,
                    threat_type: ThreatType::CommandInjection,
                    detected_at: SystemTime::now(),
                    description: "检测到命令注入尝试".to_string(),
                    evidence: vec![original.clone()],
                    recommended_actions: vec![SecurityAction::Log(LogLevel::Warn), SecurityAction::Sanitize],
                };
                threats.push(threat);

                operations.push(SanitizationOperation {
                    operation_type: SanitizationType::CommandInjectionRemoval,
                    field_path: field_path.to_string(),
                    original_value: original,
                    sanitized_value: sanitized.clone(),
                    triggered_rule: "COMMAND_INJECTION".to_string(),
                    threat_level: ThreatLevel::High,
                });

                warn!("检测到命令注入尝试，字段: {}", field_path);
            }
        }

        // 4. 路径遍历检测和净化
        for pattern in &self.path_traversal_patterns {
            if pattern.is_match(&sanitized) {
                let original = sanitized.clone();
                sanitized = pattern.replace_all(&sanitized, "[REMOVED]").to_string();
                
                let threat = ThreatInfo {
                    source: field_path.to_string(),
                    level: ThreatLevel::Medium,
                    threat_type: ThreatType::PathTraversal,
                    detected_at: SystemTime::now(),
                    description: "检测到路径遍历尝试".to_string(),
                    evidence: vec![original.clone()],
                    recommended_actions: vec![SecurityAction::Log(LogLevel::Warn), SecurityAction::Sanitize],
                };
                threats.push(threat);

                operations.push(SanitizationOperation {
                    operation_type: SanitizationType::PathNormalization,
                    field_path: field_path.to_string(),
                    original_value: original,
                    sanitized_value: sanitized.clone(),
                    triggered_rule: "PATH_TRAVERSAL".to_string(),
                    threat_level: ThreatLevel::Medium,
                });

                warn!("检测到路径遍历尝试，字段: {}", field_path);
            }
        }

        // 5. 敏感信息检测和脱敏
        for pattern in &self.sensitive_data_patterns {
            if pattern.is_match(&sanitized) {
                let original = sanitized.clone();
                sanitized = pattern.replace_all(&sanitized, "***").to_string();
                
                let threat = ThreatInfo {
                    source: field_path.to_string(),
                    level: ThreatLevel::Low,
                    threat_type: ThreatType::DataLeakage,
                    detected_at: SystemTime::now(),
                    description: "检测到敏感信息".to_string(),
                    evidence: vec!["[REDACTED]".to_string()],
                    recommended_actions: vec![SecurityAction::Log(LogLevel::Info), SecurityAction::Sanitize],
                };
                threats.push(threat);

                operations.push(SanitizationOperation {
                    operation_type: SanitizationType::SensitiveDataMasking,
                    field_path: field_path.to_string(),
                    original_value: "[REDACTED]".to_string(),
                    sanitized_value: sanitized.clone(),
                    triggered_rule: "SENSITIVE_DATA".to_string(),
                    threat_level: ThreatLevel::Low,
                });

                debug!("检测到敏感信息并脱敏，字段: {}", field_path);
            }
        }

        // 6. 应用自定义规则
        for rule in &self.custom_rules {
            if rule.enabled {
                if let Ok(pattern) = Regex::new(&rule.pattern) {
                    if pattern.is_match(&sanitized) {
                        let original = sanitized.clone();
                        sanitized = pattern.replace_all(&sanitized, &rule.replacement).to_string();
                        
                        let threat = ThreatInfo {
                            source: field_path.to_string(),
                            level: rule.threat_level,
                            threat_type: ThreatType::Unknown,
                            detected_at: SystemTime::now(),
                            description: format!("触发自定义规则: {}", rule.name),
                            evidence: vec![original.clone()],
                            recommended_actions: vec![SecurityAction::Log(LogLevel::Info), SecurityAction::Sanitize],
                        };
                        threats.push(threat);

                        operations.push(SanitizationOperation {
                            operation_type: SanitizationType::CustomRule(rule.name.clone()),
                            field_path: field_path.to_string(),
                            original_value: original,
                            sanitized_value: sanitized.clone(),
                            triggered_rule: rule.name.clone(),
                            threat_level: rule.threat_level,
                        });
                    }
                }
            }
        }

        StringSanitizationResult {
            value: sanitized,
            threats,
            operations,
        }
    }

    /// 获取SQL注入检测模式
    fn get_sql_injection_patterns() -> Vec<Regex> {
        SQL_INJECTION_PATTERNS.get_or_init(|| {
            let patterns = vec![
                // 基本SQL注入模式
                r"(?i)(union\s+select|select\s+.*\s+from|insert\s+into|update\s+.*\s+set|delete\s+from)",
                r"(?i)(drop\s+table|create\s+table|alter\s+table|truncate\s+table)",
                r"(?i)(exec\s*\(|execute\s*\(|sp_executesql)",
                r"(?i)(;|\|\||&&)\s*(select|insert|update|delete|drop|create|alter)",
                r#"(?i)('|\")?\s*(or|and)\s*('|\")?\s*1\s*=\s*1"#,
                r#"(?i)('|\")?\s*(or|and)\s*('|\")?\s*true"#,
                r#"(?i)('|\")?\s*(or|and)\s*('|\")?\s*1\s*<\s*2"#,
                r"(?i)(sleep\s*\(|benchmark\s*\(|pg_sleep\s*\()",
                r"(?i)(load_file\s*\(|into\s+outfile|into\s+dumpfile)",
                r"(?i)(waitfor\s+delay|dbms_pipe\.receive_message)",
                // 时间盲注
                r"(?i)(and\s+.*\s+sleep\s*\(|or\s+.*\s+sleep\s*\()",
                // 错误注入
                r"(?i)(extractvalue\s*\(|updatexml\s*\(|exp\s*\(.*\))",
                // NoSQL注入
                r"(?i)(\$ne|\$gt|\$lt|\$gte|\$lte|\$regex|\$where)",
                r"(?i)(this\s*\.\s*constructor|constructor\s*\.\s*prototype)",
            ];

            patterns.into_iter()
                .filter_map(|p| Regex::new(p).ok())
                .collect()
        }).clone()
    }

    /// 获取XSS攻击检测模式
    fn get_xss_patterns() -> Vec<Regex> {
        XSS_PATTERNS.get_or_init(|| {
            let patterns = vec![
                // 基本脚本标签
                r"(?i)<script[^>]*>.*?</script>",
                r"(?i)<script[^>]*>",
                r"(?i)</script>",
                // 事件处理器
                r"(?i)on(load|click|error|focus|blur|change|submit|reset|select|resize|scroll|mouseover|mouseout|mousedown|mouseup|keydown|keyup|keypress)\s*=",
                // JavaScript协议
                r"(?i)javascript\s*:",
                r"(?i)vbscript\s*:",
                // Data URI 与 Base64
                r"(?i)data\s*:\s*text/html",
                r"(?i)data\s*:\s*image/svg\+xml",
                // HTML实体编码的脚本
                r"(?i)&#[0-9]+;",
                r"(?i)&lt;script|&gt;script",
                // iframe和embed
                r"(?i)<iframe[^>]*>.*?</iframe>",
                r"(?i)<embed[^>]*>",
                r"(?i)<object[^>]*>.*?</object>",
                // 样式表攻击
                r"(?i)<style[^>]*>.*?</style>",
                r"(?i)expression\s*\(",
                r"(?i)@import",
                // 链接攻击
                r"(?i)<link[^>]*>",
                r"(?i)<meta[^>]*>",
                // 表单攻击
                r"(?i)<form[^>]*>.*?</form>",
                // 注释攻击
                r"(?i)<!--.*?-->",
                r"(?i)/\*.*?\*/",
            ];

            patterns.into_iter()
                .filter_map(|p| Regex::new(p).ok())
                .collect()
        }).clone()
    }

    /// 获取命令注入检测模式
    fn get_command_injection_patterns() -> Vec<Regex> {
        COMMAND_INJECTION_PATTERNS.get_or_init(|| {
            let patterns = vec![
                // Unix/Linux 命令
                r"(?i)(;|\|\||&&|\|)\s*(ls|cat|pwd|whoami|id|ps|netstat|ifconfig|uname)",
                r"(?i)(;|\|\||&&|\|)\s*(rm|mv|cp|chmod|chown|kill|killall)",
                r"(?i)(;|\|\||&&|\|)\s*(curl|wget|nc|telnet|ssh|ftp)",
                r"(?i)(;|\|\||&&|\|)\s*(bash|sh|zsh|csh|tcsh|fish)",
                r"(?i)(;|\|\||&&|\|)\s*(python|perl|ruby|php|node|java)",
                // Windows 命令
                r"(?i)(;|\|\||&&|\|)\s*(dir|type|copy|del|move|attrib)",
                r"(?i)(;|\|\||&&|\|)\s*(cmd|powershell|wmic|net|sc)",
                r"(?i)(;|\|\||&&|\|)\s*(ping|tracert|nslookup|ipconfig)",
                r"(?i)(;|\|\||&&|\|)\s*(tasklist|taskkill|schtasks)",
                // 重定向和管道
                r"(?i)>\s*/dev/null",
                r"(?i)>\s*nul",
                r"(?i)<\s*/dev/zero",
                r"(?i)2>&1",
                // 反引号和命令替换
                r"`[^`]*`",
                r"\$\([^)]*\)",
                // 环境变量
                r"(?i)\$PATH|\$HOME|\$USER|\$PWD",
                r"(?i)%PATH%|%HOME%|%USER%|%USERPROFILE%",
            ];

            patterns.into_iter()
                .filter_map(|p| Regex::new(p).ok())
                .collect()
        }).clone()
    }

    /// 获取路径遍历检测模式
    fn get_path_traversal_patterns() -> Vec<Regex> {
        PATH_TRAVERSAL_PATTERNS.get_or_init(|| {
            let patterns = vec![
                // 基本路径遍历
                r"\.\.[\\/]",
                r"[\\/]\.\.[\\/]",
                r"[\\/]\.\.",
                r"\.\.%2[fF]",
                r"%2[eE]%2[eE]%2[fF]",
                r"%2[eE]%2[eE][\\/]",
                // URL编码的路径遍历
                r"%2[eE]%2[eE]",
                r"%%32%65%%32%65",
                r"%%2e%%2e%%2f",
                // Unicode编码
                r"\u002e\u002e\u002f",
                r"\u002e\u002e\u005c",
                // 双重编码
                r"%252[eE]%252[eE]%252[fF]",
                r"%c0%ae%c0%ae%c0%af",
                // 绝对路径
                r"^[\\/]",
                r"^[a-zA-Z]:",
                // 系统敏感路径
                r"(?i)[\\/](etc|proc|sys|dev|boot|var|tmp)[\\/]",
                r"(?i)[\\/](windows|system32|syswow64)[\\/]",
                r"(?i)[\\/](users|documents and settings)[\\/]",
            ];

            patterns.into_iter()
                .filter_map(|p| Regex::new(p).ok())
                .collect()
        }).clone()
    }

    /// 获取敏感信息检测模式
    fn get_sensitive_data_patterns() -> Vec<Regex> {
        SENSITIVE_DATA_PATTERNS.get_or_init(|| {
            let patterns = vec![
                // 密码相关
                r"(?i)(password|passwd|pwd)\s*[:=]\s*\S+",
                r"(?i)(secret|token|key)\s*[:=]\s*\S+",
                r"(?i)api[_-]?key\s*[:=]\s*\S+",
                r"(?i)auth[_-]?token\s*[:=]\s*\S+",
                // 信用卡号
                r"\b(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|3[47][0-9]{13}|3[0-9]{13}|6(?:011|5[0-9]{2})[0-9]{12})\b",
                // 社会安全号码 (SSN)
                r"\b[0-9]{3}-[0-9]{2}-[0-9]{4}\b",
                r"\b[0-9]{9}\b",
                // 电话号码
                r"\b\d{3}[-.]?\d{3}[-.]?\d{4}\b",
                r"\+?\d{1,4}[-.\s]?\(?\d{1,4}\)?[-.\s]?\d{1,4}[-.\s]?\d{1,9}",
                // 邮箱地址
                r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b",
                // IP地址
                r"\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b",
                r"\b(?:[A-F0-9]{1,4}:){7}[A-F0-9]{1,4}\b",
                // URL包含凭据
                r"(?i)(https?|ftp)://[^:\s]+:[^@\s]+@",
                // 数据库连接字符串
                r"(?i)(server|host|database|db)\s*=\s*[^;\s]+",
                r"(?i)(user|username|uid)\s*=\s*[^;\s]+",
                r"(?i)(password|pwd)\s*=\s*[^;\s]+",
            ];

            patterns.into_iter()
                .filter_map(|p| Regex::new(p).ok())
                .collect()
        }).clone()
    }

    /// 添加自定义净化规则
    pub fn add_custom_rule(&mut self, rule: SanitizationRule) {
        self.custom_rules.push(rule);
        debug!("添加自定义净化规则: {}", self.custom_rules.last().unwrap().name);
    }

    /// 移除自定义净化规则
    pub fn remove_custom_rule(&mut self, rule_name: &str) -> bool {
        let original_len = self.custom_rules.len();
        self.custom_rules.retain(|rule| rule.name != rule_name);
        let removed = self.custom_rules.len() < original_len;
        if removed {
            debug!("移除自定义净化规则: {}", rule_name);
        }
        removed
    }

    /// 获取所有自定义规则
    pub fn get_custom_rules(&self) -> &[SanitizationRule] {
        &self.custom_rules
    }
}

/// JSON净化结果
struct JsonSanitizationResult {
    value: serde_json::Value,
    threats: Vec<ThreatInfo>,
    operations: Vec<SanitizationOperation>,
}

/// 字符串净化结果
struct StringSanitizationResult {
    value: String,
    threats: Vec<ThreatInfo>,
    operations: Vec<SanitizationOperation>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::protocol::message::{MessageType, MessagePriority};

    fn create_test_config() -> ProtocolGuardConfig {
        ProtocolGuardConfig::default()
    }

    fn create_test_message_with_payload(payload: serde_json::Value) -> NativeMessage {
        NativeMessage {
            version: 1,
            message_type: MessageType::Test,
            request_id: "test-123".to_string(),
            payload,
            timestamp: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(),
            source: "test-extension".to_string(),
            priority: MessagePriority::Normal,
            timeout_ms: Some(30000),
            metadata: HashMap::new(),
            extensions: HashMap::new(),
        }
    }

    #[tokio::test]
    async fn test_sanitizer_creation() {
        let config = create_test_config();
        let sanitizer = MessageSanitizer::new(config).await;
        assert!(sanitizer.is_ok());
    }

    #[tokio::test]
    async fn test_clean_message_unchanged() {
        let config = create_test_config();
        let sanitizer = MessageSanitizer::new(config).await.unwrap();
        
        let message = create_test_message_with_payload(serde_json::json!({
            "user": "john",
            "action": "login"
        }));
        
        let result = sanitizer.sanitize_message(&message).await.unwrap();
        assert!(!result.was_modified);
        assert!(result.detected_threats.is_empty());
    }

    #[tokio::test]
    async fn test_sql_injection_detection() {
        let config = create_test_config();
        let sanitizer = MessageSanitizer::new(config).await.unwrap();
        
        let message = create_test_message_with_payload(serde_json::json!({
            "query": "SELECT * FROM users WHERE id = 1 OR 1=1"
        }));
        
        let result = sanitizer.sanitize_message(&message).await.unwrap();
        assert!(result.was_modified);
        assert!(!result.detected_threats.is_empty());
        assert!(result.detected_threats.iter().any(|t| t.threat_type == ThreatType::SqlInjection));
    }

    #[tokio::test]
    async fn test_xss_detection() {
        let config = create_test_config();
        let sanitizer = MessageSanitizer::new(config).await.unwrap();
        
        let message = create_test_message_with_payload(serde_json::json!({
            "content": "<script>alert('xss')</script>"
        }));
        
        let result = sanitizer.sanitize_message(&message).await.unwrap();
        assert!(result.was_modified);
        assert!(!result.detected_threats.is_empty());
        assert!(result.detected_threats.iter().any(|t| t.threat_type == ThreatType::CrossSiteScripting));
    }

    #[tokio::test]
    async fn test_command_injection_detection() {
        let config = create_test_config();
        let sanitizer = MessageSanitizer::new(config).await.unwrap();
        
        let message = create_test_message_with_payload(serde_json::json!({
            "file": "test.txt; rm -rf /"
        }));
        
        let result = sanitizer.sanitize_message(&message).await.unwrap();
        assert!(result.was_modified);
        assert!(!result.detected_threats.is_empty());
        assert!(result.detected_threats.iter().any(|t| t.threat_type == ThreatType::CommandInjection));
    }

    #[tokio::test]
    async fn test_path_traversal_detection() {
        let config = create_test_config();
        let sanitizer = MessageSanitizer::new(config).await.unwrap();
        
        let message = create_test_message_with_payload(serde_json::json!({
            "path": "../../../etc/passwd"
        }));
        
        let result = sanitizer.sanitize_message(&message).await.unwrap();
        assert!(result.was_modified);
        assert!(!result.detected_threats.is_empty());
        assert!(result.detected_threats.iter().any(|t| t.threat_type == ThreatType::PathTraversal));
    }

    #[tokio::test]
    async fn test_sensitive_data_masking() {
        let config = create_test_config();
        let sanitizer = MessageSanitizer::new(config).await.unwrap();
        
        let message = create_test_message_with_payload(serde_json::json!({
            "user": "john",
            "password": "secret123"
        }));
        
        let result = sanitizer.sanitize_message(&message).await.unwrap();
        assert!(result.was_modified);
        assert!(!result.detected_threats.is_empty());
        assert!(result.detected_threats.iter().any(|t| t.threat_type == ThreatType::DataLeakage));
    }

    #[tokio::test]
    async fn test_custom_rule() {
        let config = create_test_config();
        let mut sanitizer = MessageSanitizer::new(config).await.unwrap();
        
        // 添加自定义规则
        sanitizer.add_custom_rule(SanitizationRule {
            name: "test_rule".to_string(),
            pattern: r"FORBIDDEN".to_string(),
            replacement: "[BLOCKED]".to_string(),
            threat_level: ThreatLevel::Medium,
            enabled: true,
        });
        
        let message = create_test_message_with_payload(serde_json::json!({
            "content": "This contains FORBIDDEN content"
        }));
        
        let result = sanitizer.sanitize_message(&message).await.unwrap();
        assert!(result.was_modified);
        assert!(result.sanitized_message.payload.to_string().contains("[BLOCKED]"));
    }

    #[tokio::test]
    async fn test_nested_object_sanitization() {
        let config = create_test_config();
        let sanitizer = MessageSanitizer::new(config).await.unwrap();
        
        let message = create_test_message_with_payload(serde_json::json!({
            "user": {
                "name": "john",
                "query": "SELECT * FROM users WHERE id = 1 OR 1=1",
                "settings": {
                    "theme": "dark",
                    "script": "<script>alert('xss')</script>"
                }
            }
        }));
        
        let result = sanitizer.sanitize_message(&message).await.unwrap();
        assert!(result.was_modified);
        assert!(!result.detected_threats.is_empty());
        assert!(result.detected_threats.len() >= 2); // SQL注入 + XSS
    }

    #[tokio::test]
    async fn test_array_sanitization() {
        let config = create_test_config();
        let sanitizer = MessageSanitizer::new(config).await.unwrap();
        
        let message = create_test_message_with_payload(serde_json::json!({
            "queries": [
                "SELECT name FROM users",
                "SELECT * FROM users WHERE id = 1 OR 1=1",
                "UPDATE users SET password = 'hacked'"
            ]
        }));
        
        let result = sanitizer.sanitize_message(&message).await.unwrap();
        assert!(result.was_modified);
        assert!(!result.detected_threats.is_empty());
    }
} 