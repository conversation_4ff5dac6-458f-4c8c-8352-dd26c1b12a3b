//! Native Messaging 协议安全防护器模块
//!
//! 提供协议层安全防护功能，包括：
//! - 消息净化和格式验证
//! - 协议版本安全控制  
//! - 请求频率限制和DoS防护
//! - 加密通信和完整性验证

use crate::native_messaging::error::{NativeMessagingError, Result};
use crate::native_messaging::protocol::message::NativeMessage;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

pub mod encryption_guard;
pub mod message_sanitizer;
pub mod protocol_validator;
pub mod rate_limiter;

// 重新导出核心类型
pub use encryption_guard::{EncryptionGuard, EncryptionGuardConfig, EncryptionLevel};
pub use message_sanitizer::{MessageSanitizer, SanitizationResult};
pub use protocol_validator::ProtocolValidator;
pub use rate_limiter::{RateLimiter, RateLimitConfig};

/// 协议安全防护器主结构
pub struct ProtocolGuard {
    /// 消息净化器
    message_sanitizer: MessageSanitizer,
    /// 协议验证器
    protocol_validator: ProtocolValidator,
    /// 频率限制器
    rate_limiter: RateLimiter,
    /// 加密防护器
    encryption_guard: EncryptionGuard,
    /// 配置信息
    config: ProtocolGuardConfig,
    /// 运行时统计
    stats: Arc<RwLock<ProtocolGuardStats>>,
    /// 威胁检测缓存
    threat_cache: Arc<RwLock<HashMap<String, ThreatInfo>>>,
}

/// 协议防护器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProtocolGuardConfig {
    /// 安全级别
    pub security_level: ProtocolSecurityLevel,
    /// 是否启用消息净化
    pub enable_sanitization: bool,
    /// 是否启用协议验证
    pub enable_protocol_validation: bool,
    /// 是否启用频率限制
    pub enable_rate_limiting: bool,
    /// 是否启用加密防护
    pub enable_encryption_guard: bool,
    /// 最大消息大小（字节）
    pub max_message_size: usize,
    /// 消息超时时间
    pub message_timeout: Duration,
    /// 威胁检测敏感度
    pub threat_detection_sensitivity: f64,
    /// 缓存清理间隔
    pub cache_cleanup_interval: Duration,
    /// 统计报告间隔
    pub stats_report_interval: Duration,
}

/// 协议安全级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ProtocolSecurityLevel {
    /// 基础级别 - 基本验证和净化
    Basic,
    /// 标准级别 - 包含频率限制
    Standard,
    /// 高级级别 - 包含威胁检测
    Advanced,
    /// 企业级别 - 全面安全防护
    Enterprise,
}

/// 安全规则定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityRule {
    /// 规则ID
    pub id: String,
    /// 规则名称
    pub name: String,
    /// 规则类型
    pub rule_type: SecurityRuleType,
    /// 匹配条件
    pub conditions: Vec<SecurityCondition>,
    /// 执行动作
    pub actions: Vec<SecurityAction>,
    /// 优先级（数值越大优先级越高）
    pub priority: u32,
    /// 是否启用
    pub enabled: bool,
    /// 创建时间
    pub created_at: SystemTime,
    /// 最后更新时间
    pub updated_at: SystemTime,
}

/// 安全规则类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SecurityRuleType {
    /// 消息验证规则
    MessageValidation,
    /// 频率限制规则
    RateLimit,
    /// 威胁检测规则
    ThreatDetection,
    /// 访问控制规则
    AccessControl,
    /// 内容过滤规则
    ContentFilter,
}

/// 安全条件定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityCondition {
    /// 字段名称
    pub field: String,
    /// 操作符
    pub operator: ConditionOperator,
    /// 期望值
    pub value: serde_json::Value,
    /// 是否取反
    pub negate: bool,
}

/// 条件操作符
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ConditionOperator {
    /// 等于
    Equals,
    /// 不等于
    NotEquals,
    /// 包含
    Contains,
    /// 不包含
    NotContains,
    /// 匹配正则表达式
    Matches,
    /// 不匹配正则表达式
    NotMatches,
    /// 大于
    GreaterThan,
    /// 小于
    LessThan,
    /// 大于等于
    GreaterThanOrEqual,
    /// 小于等于
    LessThanOrEqual,
    /// 在列表中
    In,
    /// 不在列表中
    NotIn,
}

/// 安全动作定义
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SecurityAction {
    /// 允许通过
    Allow,
    /// 拒绝请求
    Deny,
    /// 记录日志
    Log(LogLevel),
    /// 发送告警
    Alert(AlertLevel),
    /// 限制频率
    RateLimit(Duration),
    /// 净化消息
    Sanitize,
    /// 加密消息
    Encrypt,
    /// 隔离来源
    Quarantine(Duration),
    /// 自定义动作
    Custom(String),
}

/// 日志级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum LogLevel {
    Debug,
    Info,
    Warn,
    Error,
}

/// 告警级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AlertLevel {
    Low,
    Medium,
    High,
    Critical,
}

/// 威胁级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum ThreatLevel {
    /// 无威胁
    None = 0,
    /// 低威胁
    Low = 1,
    /// 中等威胁
    Medium = 2,
    /// 高威胁
    High = 3,
    /// 严重威胁
    Critical = 4,
}

/// 威胁信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreatInfo {
    /// 威胁来源
    pub source: String,
    /// 威胁级别
    pub level: ThreatLevel,
    /// 威胁类型
    pub threat_type: ThreatType,
    /// 检测时间
    pub detected_at: SystemTime,
    /// 威胁描述
    pub description: String,
    /// 相关证据
    pub evidence: Vec<String>,
    /// 建议动作
    pub recommended_actions: Vec<SecurityAction>,
}

/// 威胁类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ThreatType {
    /// SQL注入攻击
    SqlInjection,
    /// XSS攻击
    CrossSiteScripting,
    /// 命令注入
    CommandInjection,
    /// 路径遍历
    PathTraversal,
    /// DoS攻击
    DenialOfService,
    /// 暴力破解
    BruteForce,
    /// 数据泄露
    DataLeakage,
    /// 权限提升
    PrivilegeEscalation,
    /// 恶意负载
    MaliciousPayload,
    /// 异常行为
    AnomalousBehavior,
    /// 未知威胁
    Unknown,
}

/// 协议防护器统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProtocolGuardStats {
    /// 处理的消息总数
    pub total_messages: u64,
    /// 通过验证的消息数
    pub passed_messages: u64,
    /// 被拒绝的消息数
    pub rejected_messages: u64,
    /// 被净化的消息数
    pub sanitized_messages: u64,
    /// 检测到的威胁数
    pub threats_detected: u64,
    /// 威胁统计详情
    pub threat_stats: HashMap<ThreatType, u64>,
    /// 平均处理时间（微秒）
    pub avg_processing_time_micros: u64,
    /// 最大处理时间（微秒）
    pub max_processing_time_micros: u64,
    /// 统计开始时间
    pub stats_start_time: SystemTime,
    /// 最后更新时间
    pub last_updated: SystemTime,
}

impl Default for ProtocolGuardStats {
    fn default() -> Self {
        let now = SystemTime::now();
        Self {
            total_messages: 0,
            passed_messages: 0,
            rejected_messages: 0,
            sanitized_messages: 0,
            threats_detected: 0,
            threat_stats: HashMap::new(),
            avg_processing_time_micros: 0,
            max_processing_time_micros: 0,
            stats_start_time: now,
            last_updated: now,
        }
    }
}

/// 安全验证结果
#[derive(Debug, Clone)]
pub struct SecurityValidationResult {
    /// 验证是否通过
    pub is_valid: bool,
    /// 安全级别
    pub security_level: ProtocolSecurityLevel,
    /// 威胁级别
    pub threat_level: ThreatLevel,
    /// 检测到的威胁列表
    pub detected_threats: Vec<ThreatInfo>,
    /// 应用的安全动作
    pub applied_actions: Vec<SecurityAction>,
    /// 验证错误信息
    pub errors: Vec<String>,
    /// 警告信息
    pub warnings: Vec<String>,
    /// 验证耗时（微秒）
    pub processing_time_micros: u64,
    /// 验证时间戳
    pub validated_at: SystemTime,
}

impl Default for ProtocolGuardConfig {
    fn default() -> Self {
        Self {
            security_level: ProtocolSecurityLevel::Standard,
            enable_sanitization: true,
            enable_protocol_validation: true,
            enable_rate_limiting: true,
            enable_encryption_guard: true,
            max_message_size: 1024 * 1024, // 1MB
            message_timeout: Duration::from_secs(30),
            threat_detection_sensitivity: 0.7,
            cache_cleanup_interval: Duration::from_secs(300), // 5分钟
            stats_report_interval: Duration::from_secs(60),   // 1分钟
        }
    }
}

impl ProtocolGuard {
    /// 创建新的协议防护器
    ///
    /// # 参数
    /// - `config`: 协议防护器配置
    ///
    /// # 返回
    /// Result<ProtocolGuard> - 创建的防护器实例
    pub async fn new(config: ProtocolGuardConfig) -> Result<Self> {
        info!("初始化协议安全防护器，安全级别: {:?}", config.security_level);

        let message_sanitizer = MessageSanitizer::new(config.clone()).await?;
        let protocol_validator = ProtocolValidator::new();
        let rate_limiter = RateLimiter::new(RateLimitConfig::default());
        let encryption_guard = EncryptionGuard::new(EncryptionGuardConfig::default())?;

        let guard = Self {
            message_sanitizer,
            protocol_validator,
            rate_limiter,
            encryption_guard,
            config: config.clone(),
            stats: Arc::new(RwLock::new(ProtocolGuardStats::default())),
            threat_cache: Arc::new(RwLock::new(HashMap::new())),
        };

        // 启动后台清理任务
        guard.start_background_tasks().await?;

        info!("协议安全防护器初始化完成");
        Ok(guard)
    }

    /// 验证和净化消息
    ///
    /// # 参数
    /// - `message`: 待验证的消息
    ///
    /// # 返回
    /// Result<SecurityValidationResult> - 验证结果
    pub async fn validate_and_sanitize(&self, message: &NativeMessage) -> Result<SecurityValidationResult> {
        let start_time = Instant::now();
        let mut result = SecurityValidationResult {
            is_valid: true,
            security_level: self.config.security_level,
            threat_level: ThreatLevel::None,
            detected_threats: Vec::new(),
            applied_actions: Vec::new(),
            errors: Vec::new(),
            warnings: Vec::new(),
            processing_time_micros: 0,
            validated_at: SystemTime::now(),
        };

        // 1. 基础大小检查
        if let Err(e) = self.validate_message_size(message) {
            result.is_valid = false;
            result.errors.push(e.to_string());
            result.threat_level = ThreatLevel::Medium;
            return Ok(result);
        }

        // 2. 频率限制检查
        if self.config.enable_rate_limiting {
            match self.rate_limiter.check_request(message).await {
                Ok(rate_result) => {
                    if !rate_result.allowed {
                        result.is_valid = false;
                        result.errors.push(rate_result.reason);
                        result.threat_level = ThreatLevel::Medium;
                        result.applied_actions.push(SecurityAction::RateLimit(Duration::from_secs(60)));
                        
                        self.record_threat(&message.source, ThreatType::DenialOfService, 
                                         "频率限制触发".to_string()).await;
                        return Ok(result);
                    }
                }
                Err(e) => {
                    result.warnings.push(format!("频率检查失败: {}", e));
                }
            }
        }

        // 3. 协议验证
        if self.config.enable_protocol_validation {
            // 将 NativeMessage 转换为 Value 进行验证
            let message_value = serde_json::to_value(message)
                .map_err(|e| NativeMessagingError::SerializationError(e))?;
            
            match self.protocol_validator.validate_message_format(&message_value).await {
                Ok(validation_result) => {
                    if !validation_result.is_valid {
                        result.is_valid = false;
                        result.errors.extend(validation_result.errors);
                        result.warnings.extend(validation_result.warnings);
                        result.threat_level = std::cmp::max(result.threat_level, ThreatLevel::Low);
                    }
                }
                Err(e) => {
                    result.is_valid = false;
                    result.errors.push(format!("协议验证失败: {}", e));
                    result.threat_level = ThreatLevel::High;
                }
            }
        }

        // 4. 消息净化
        if self.config.enable_sanitization {
            match self.message_sanitizer.sanitize_message(message).await {
                Ok(sanitization_result) => {
                    if sanitization_result.was_modified {
                        result.applied_actions.push(SecurityAction::Sanitize);
                        result.warnings.push("消息已被净化".to_string());
                    }
                    
                    // 检查净化过程中发现的威胁
                    for threat in &sanitization_result.detected_threats {
                        result.detected_threats.push(threat.clone());
                        result.threat_level = std::cmp::max(result.threat_level, threat.level);
                    }
                }
                Err(e) => {
                    result.warnings.push(format!("消息净化失败: {}", e));
                }
            }
        }

        // 5. 加密防护检查
        if self.config.enable_encryption_guard {
            match self.encryption_guard.validate_config() {
                Ok(_) => {
                    // 配置验证通过
                }
                Err(e) => {
                    result.warnings.push(format!("加密配置验证失败: {}", e));
                }
            }
        }

        // 6. 威胁级别评估
        if result.threat_level >= ThreatLevel::Medium {
            self.record_threat(&message.source, ThreatType::AnomalousBehavior,
                             format!("检测到威胁级别: {:?}", result.threat_level)).await;
        }

        // 7. 更新统计信息
        let processing_time = start_time.elapsed();
        result.processing_time_micros = processing_time.as_micros() as u64;
        self.update_stats(&result).await;

        debug!(
            "消息安全验证完成: valid={}, threat_level={:?}, time={}µs",
            result.is_valid, result.threat_level, result.processing_time_micros
        );

        Ok(result)
    }

    /// 验证消息大小
    fn validate_message_size(&self, message: &NativeMessage) -> Result<()> {
        let message_size = serde_json::to_vec(message)
            .map_err(|e| NativeMessagingError::SerializationError(e))?
            .len();

        if message_size > self.config.max_message_size {
            return Err(NativeMessagingError::ProtocolError(
                format!("消息大小 {} 超过限制 {}", message_size, self.config.max_message_size)
            ));
        }

        Ok(())
    }

    /// 记录威胁信息
    async fn record_threat(&self, source: &str, threat_type: ThreatType, description: String) {
        let threat_info = ThreatInfo {
            source: source.to_string(),
            level: match threat_type {
                ThreatType::SqlInjection | ThreatType::CommandInjection => ThreatLevel::High,
                ThreatType::CrossSiteScripting | ThreatType::PathTraversal => ThreatLevel::Medium,
                ThreatType::DenialOfService | ThreatType::BruteForce => ThreatLevel::Medium,
                _ => ThreatLevel::Low,
            },
            threat_type: threat_type.clone(),
            detected_at: SystemTime::now(),
            description,
            evidence: Vec::new(),
            recommended_actions: vec![SecurityAction::Log(LogLevel::Warn)],
        };

        let mut cache = self.threat_cache.write().await;
        cache.insert(format!("{}_{:?}", source, threat_type), threat_info);

        warn!("检测到威胁: 来源={}, 类型={:?}", source, threat_type);
    }

    /// 更新统计信息
    async fn update_stats(&self, result: &SecurityValidationResult) {
        let mut stats = self.stats.write().await;
        stats.total_messages += 1;
        stats.last_updated = SystemTime::now();

        if result.is_valid {
            stats.passed_messages += 1;
        } else {
            stats.rejected_messages += 1;
        }

        if result.applied_actions.contains(&SecurityAction::Sanitize) {
            stats.sanitized_messages += 1;
        }

        if !result.detected_threats.is_empty() {
            stats.threats_detected += result.detected_threats.len() as u64;
            
            for threat in &result.detected_threats {
                *stats.threat_stats.entry(threat.threat_type.clone()).or_insert(0) += 1;
            }
        }

        // 更新处理时间统计
        if stats.total_messages == 1 {
            stats.avg_processing_time_micros = result.processing_time_micros;
            stats.max_processing_time_micros = result.processing_time_micros;
        } else {
            stats.avg_processing_time_micros = 
                (stats.avg_processing_time_micros * (stats.total_messages - 1) + result.processing_time_micros) 
                / stats.total_messages;
            stats.max_processing_time_micros = 
                std::cmp::max(stats.max_processing_time_micros, result.processing_time_micros);
        }
    }

    /// 启动后台任务
    async fn start_background_tasks(&self) -> Result<()> {
        // 缓存清理任务
        let threat_cache = Arc::clone(&self.threat_cache);
        let cleanup_interval = self.config.cache_cleanup_interval;
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(cleanup_interval);
            loop {
                interval.tick().await;
                Self::cleanup_threat_cache(&threat_cache).await;
            }
        });

        // 统计报告任务
        let stats = Arc::clone(&self.stats);
        let report_interval = self.config.stats_report_interval;
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(report_interval);
            loop {
                interval.tick().await;
                Self::report_stats(&stats).await;
            }
        });

        Ok(())
    }

    /// 清理威胁缓存
    async fn cleanup_threat_cache(threat_cache: &Arc<RwLock<HashMap<String, ThreatInfo>>>) {
        let mut cache = threat_cache.write().await;
        let cutoff_time = SystemTime::now() - Duration::from_secs(3600); // 1小时前
        
        cache.retain(|_, threat_info| {
            threat_info.detected_at > cutoff_time
        });
        
        debug!("威胁缓存清理完成，当前条目数: {}", cache.len());
    }

    /// 报告统计信息
    async fn report_stats(stats: &Arc<RwLock<ProtocolGuardStats>>) {
        let stats_snapshot = stats.read().await.clone();
        
        if stats_snapshot.total_messages > 0 {
            info!(
                "协议防护器统计: 总消息={}, 通过={}, 拒绝={}, 威胁={}, 平均处理时间={}µs",
                stats_snapshot.total_messages,
                stats_snapshot.passed_messages,
                stats_snapshot.rejected_messages,
                stats_snapshot.threats_detected,
                stats_snapshot.avg_processing_time_micros
            );
        }
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> ProtocolGuardStats {
        self.stats.read().await.clone()
    }

    /// 获取威胁信息
    pub async fn get_threats(&self) -> Vec<ThreatInfo> {
        self.threat_cache.read().await.values().cloned().collect()
    }

    /// 清理所有缓存和统计
    pub async fn clear_all_data(&self) {
        self.threat_cache.write().await.clear();
        *self.stats.write().await = ProtocolGuardStats::default();
        info!("协议防护器数据已清理");
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::protocol::message::MessageType;
    use std::time::UNIX_EPOCH;

    fn create_test_message() -> NativeMessage {
        NativeMessage {
            version: 1,
            message_type: MessageType::Custom("test".to_string()),
            request_id: "test-123".to_string(),
            payload: serde_json::json!({"data": "test"}),
            timestamp: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(),
            source: "test-extension".to_string(),
            priority: crate::native_messaging::protocol::message::MessagePriority::Normal,
            timeout_ms: Some(30000),
            metadata: HashMap::new(),
            extensions: HashMap::new(),
        }
    }

    #[tokio::test]
    async fn test_protocol_guard_creation() {
        let config = ProtocolGuardConfig::default();
        let guard = ProtocolGuard::new(config).await;
        assert!(guard.is_ok());
    }

    #[tokio::test]
    async fn test_message_validation() {
        let config = ProtocolGuardConfig::default();
        let guard = ProtocolGuard::new(config).await.unwrap();
        
        let message = create_test_message();
        let result = guard.validate_and_sanitize(&message).await;
        
        assert!(result.is_ok());
        let validation_result = result.unwrap();
        assert!(validation_result.is_valid);
    }

    #[tokio::test]
    async fn test_oversized_message_rejection() {
        let mut config = ProtocolGuardConfig::default();
        config.max_message_size = 100; // 很小的限制
        
        let guard = ProtocolGuard::new(config).await.unwrap();
        
        let mut message = create_test_message();
        message.payload = serde_json::json!({"large_data": "x".repeat(1000)});
        
        let result = guard.validate_and_sanitize(&message).await.unwrap();
        assert!(!result.is_valid);
        assert!(!result.errors.is_empty());
    }

    #[tokio::test]
    async fn test_stats_collection() {
        let config = ProtocolGuardConfig::default();
        let guard = ProtocolGuard::new(config).await.unwrap();
        
        let message = create_test_message();
        let _ = guard.validate_and_sanitize(&message).await;
        
        let stats = guard.get_stats().await;
        assert_eq!(stats.total_messages, 1);
        assert_eq!(stats.passed_messages, 1);
    }
} 