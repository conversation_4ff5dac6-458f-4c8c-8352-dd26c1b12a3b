//! 频率限制器模块
//!
//! 负责限制请求频率，防止DoS攻击和资源滥用

use crate::native_messaging::error::Result;
use crate::native_messaging::protocol::message::NativeMessage;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::debug;

/// 频率限制配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    /// 启用频率限制
    pub enabled: bool,
    /// 全局限制配置
    pub global_limit: LimitRule,
    /// 按来源限制配置
    pub per_source_limit: LimitRule,
    /// 按消息类型限制配置
    pub per_message_type_limits: HashMap<String, LimitRule>,
    /// 突发限制配置
    pub burst_limit: BurstLimitConfig,
    /// 清理间隔（秒）
    pub cleanup_interval: u64,
    /// 黑名单配置
    pub blacklist_config: BlacklistConfig,
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        let mut per_message_type_limits = HashMap::new();
        per_message_type_limits.insert("get_credentials".to_string(), LimitRule {
            requests_per_window: 10,
            window_duration: Duration::from_secs(60),
            max_concurrent: 3,
        });
        per_message_type_limits.insert("store_credentials".to_string(), LimitRule {
            requests_per_window: 5,
            window_duration: Duration::from_secs(60),
            max_concurrent: 2,
        });

        Self {
            enabled: true,
            global_limit: LimitRule {
                requests_per_window: 100,
                window_duration: Duration::from_secs(60),
                max_concurrent: 10,
            },
            per_source_limit: LimitRule {
                requests_per_window: 20,
                window_duration: Duration::from_secs(60),
                max_concurrent: 5,
            },
            per_message_type_limits,
            burst_limit: BurstLimitConfig::default(),
            cleanup_interval: 300, // 5分钟
            blacklist_config: BlacklistConfig::default(),
        }
    }
}

/// 限制规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LimitRule {
    /// 时间窗口内允许的请求数
    pub requests_per_window: u32,
    /// 时间窗口持续时间
    pub window_duration: Duration,
    /// 最大并发请求数
    pub max_concurrent: u32,
}

/// 突发限制配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BurstLimitConfig {
    /// 启用突发检测
    pub enabled: bool,
    /// 突发阈值（短时间内的请求数）
    pub burst_threshold: u32,
    /// 突发检测窗口（秒）
    pub burst_window_seconds: u64,
    /// 突发惩罚时间（秒）
    pub burst_penalty_seconds: u64,
}

impl Default for BurstLimitConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            burst_threshold: 50,
            burst_window_seconds: 10,
            burst_penalty_seconds: 300, // 5分钟
        }
    }
}

/// 黑名单配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlacklistConfig {
    /// 启用自动黑名单
    pub enabled: bool,
    /// 触发黑名单的违规次数
    pub violation_threshold: u32,
    /// 黑名单持续时间（秒）
    pub blacklist_duration: u64,
    /// 手动黑名单
    pub manual_blacklist: Vec<String>,
}

impl Default for BlacklistConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            violation_threshold: 5,
            blacklist_duration: 3600, // 1小时
            manual_blacklist: Vec::new(),
        }
    }
}

/// 频率限制器
#[derive(Debug)]
pub struct RateLimiter {
    config: RateLimitConfig,
    global_tracker: RwLock<RequestTracker>,
    source_trackers: RwLock<HashMap<String, RequestTracker>>,
    message_type_trackers: RwLock<HashMap<String, RequestTracker>>,
    blacklist: RwLock<HashMap<String, Instant>>,
    last_cleanup: RwLock<Instant>,
    stats: RwLock<RateLimitStats>,
}

impl RateLimiter {
    /// 创建新的频率限制器
    pub fn new(config: RateLimitConfig) -> Self {
        let mut blacklist = HashMap::new();
        
        // 添加手动黑名单
        let now = Instant::now();
        for source in &config.blacklist_config.manual_blacklist {
            blacklist.insert(source.clone(), now + Duration::from_secs(config.blacklist_config.blacklist_duration));
        }

        Self {
            config,
            global_tracker: RwLock::new(RequestTracker::new()),
            source_trackers: RwLock::new(HashMap::new()),
            message_type_trackers: RwLock::new(HashMap::new()),
            blacklist: RwLock::new(blacklist),
            last_cleanup: RwLock::new(Instant::now()),
            stats: RwLock::new(RateLimitStats::default()),
        }
    }

    /// 检查请求是否被允许
    pub async fn check_request(&self, message: &NativeMessage) -> Result<RateLimitResult> {
        if !self.config.enabled {
            return Ok(RateLimitResult {
                allowed: true,
                reason: "频率限制已禁用".to_string(),
                retry_after: None,
                current_usage: RateLimitUsage::default(),
            });
        }

        debug!("检查频率限制: {} from {}", message.request_id, message.source);

        // 定期清理
        self.cleanup_if_needed().await;

        // 1. 检查黑名单
        if let Some(blacklist_result) = self.check_blacklist(&message.source).await {
            return Ok(blacklist_result);
        }

        // 2. 检查全局限制
        if let Some(global_result) = self.check_global_limit().await? {
            return Ok(global_result);
        }

        // 3. 检查来源限制
        if let Some(source_result) = self.check_source_limit(&message.source).await? {
            return Ok(source_result);
        }

        // 4. 检查消息类型限制
        let message_type = format!("{:?}", message.message_type).to_lowercase();
        if let Some(type_result) = self.check_message_type_limit(&message_type).await? {
            return Ok(type_result);
        }

        // 5. 检查突发限制
        if let Some(burst_result) = self.check_burst_limit(&message.source).await? {
            return Ok(burst_result);
        }

        // 记录请求
        self.record_request(message).await?;

        // 更新统计信息
        {
            let mut stats = self.stats.write().await;
            stats.total_requests += 1;
            stats.allowed_requests += 1;
        }

        Ok(RateLimitResult {
            allowed: true,
            reason: "请求已允许".to_string(),
            retry_after: None,
            current_usage: self.get_current_usage(&message.source, &message_type).await,
        })
    }

    /// 检查黑名单
    async fn check_blacklist(&self, source: &str) -> Option<RateLimitResult> {
        let blacklist = self.blacklist.read().await;
        if let Some(&blacklist_until) = blacklist.get(source) {
            if Instant::now() < blacklist_until {
                let retry_after = blacklist_until.duration_since(Instant::now());
                return Some(RateLimitResult {
                    allowed: false,
                    reason: "来源在黑名单中".to_string(),
                    retry_after: Some(retry_after),
                    current_usage: RateLimitUsage::default(),
                });
            }
        }
        None
    }

    /// 检查全局限制
    async fn check_global_limit(&self) -> Result<Option<RateLimitResult>> {
        let mut global_tracker = self.global_tracker.write().await;
        let now = Instant::now();
        
        // 清理过期请求
        global_tracker.cleanup_expired_requests(now, self.config.global_limit.window_duration);
        
        // 检查请求数限制
        if global_tracker.request_count >= self.config.global_limit.requests_per_window {
            return Ok(Some(RateLimitResult {
                allowed: false,
                reason: "全局请求频率超限".to_string(),
                retry_after: Some(self.config.global_limit.window_duration),
                current_usage: RateLimitUsage {
                    requests_in_window: global_tracker.request_count,
                    window_duration: self.config.global_limit.window_duration,
                    concurrent_requests: global_tracker.concurrent_count,
                },
            }));
        }

        // 检查并发数限制
        if global_tracker.concurrent_count >= self.config.global_limit.max_concurrent {
            return Ok(Some(RateLimitResult {
                allowed: false,
                reason: "全局并发请求数超限".to_string(),
                retry_after: Some(Duration::from_secs(1)),
                current_usage: RateLimitUsage {
                    requests_in_window: global_tracker.request_count,
                    window_duration: self.config.global_limit.window_duration,
                    concurrent_requests: global_tracker.concurrent_count,
                },
            }));
        }

        Ok(None)
    }

    /// 检查来源限制
    async fn check_source_limit(&self, source: &str) -> Result<Option<RateLimitResult>> {
        let mut source_trackers = self.source_trackers.write().await;
        let tracker = source_trackers.entry(source.to_string()).or_insert_with(RequestTracker::new);
        let now = Instant::now();
        
        tracker.cleanup_expired_requests(now, self.config.per_source_limit.window_duration);
        
        if tracker.request_count >= self.config.per_source_limit.requests_per_window {
            return Ok(Some(RateLimitResult {
                allowed: false,
                reason: format!("来源 {} 请求频率超限", source),
                retry_after: Some(self.config.per_source_limit.window_duration),
                current_usage: RateLimitUsage {
                    requests_in_window: tracker.request_count,
                    window_duration: self.config.per_source_limit.window_duration,
                    concurrent_requests: tracker.concurrent_count,
                },
            }));
        }

        if tracker.concurrent_count >= self.config.per_source_limit.max_concurrent {
            return Ok(Some(RateLimitResult {
                allowed: false,
                reason: format!("来源 {} 并发请求数超限", source),
                retry_after: Some(Duration::from_secs(1)),
                current_usage: RateLimitUsage {
                    requests_in_window: tracker.request_count,
                    window_duration: self.config.per_source_limit.window_duration,
                    concurrent_requests: tracker.concurrent_count,
                },
            }));
        }

        Ok(None)
    }

    /// 检查消息类型限制
    async fn check_message_type_limit(&self, message_type: &str) -> Result<Option<RateLimitResult>> {
        if let Some(limit_rule) = self.config.per_message_type_limits.get(message_type) {
            let mut type_trackers = self.message_type_trackers.write().await;
            let tracker = type_trackers.entry(message_type.to_string()).or_insert_with(RequestTracker::new);
            let now = Instant::now();
            
            tracker.cleanup_expired_requests(now, limit_rule.window_duration);
            
            if tracker.request_count >= limit_rule.requests_per_window {
                return Ok(Some(RateLimitResult {
                    allowed: false,
                    reason: format!("消息类型 {} 请求频率超限", message_type),
                    retry_after: Some(limit_rule.window_duration),
                    current_usage: RateLimitUsage {
                        requests_in_window: tracker.request_count,
                        window_duration: limit_rule.window_duration,
                        concurrent_requests: tracker.concurrent_count,
                    },
                }));
            }
        }

        Ok(None)
    }

    /// 检查突发限制
    async fn check_burst_limit(&self, source: &str) -> Result<Option<RateLimitResult>> {
        if !self.config.burst_limit.enabled {
            return Ok(None);
        }

        let source_trackers = self.source_trackers.read().await;
        if let Some(tracker) = source_trackers.get(source) {
            let burst_window = Duration::from_secs(self.config.burst_limit.burst_window_seconds);
            let recent_requests = tracker.count_requests_in_window(Instant::now(), burst_window);
            
            if recent_requests >= self.config.burst_limit.burst_threshold {
                // 添加到黑名单
                drop(source_trackers);
                let mut blacklist = self.blacklist.write().await;
                let penalty_until = Instant::now() + Duration::from_secs(self.config.burst_limit.burst_penalty_seconds);
                blacklist.insert(source.to_string(), penalty_until);
                
                return Ok(Some(RateLimitResult {
                    allowed: false,
                    reason: format!("来源 {} 触发突发限制", source),
                    retry_after: Some(Duration::from_secs(self.config.burst_limit.burst_penalty_seconds)),
                    current_usage: RateLimitUsage::default(),
                }));
            }
        }

        Ok(None)
    }

    /// 记录请求
    async fn record_request(&self, message: &NativeMessage) -> Result<()> {
        let now = Instant::now();

        // 记录全局请求
        {
            let mut global_tracker = self.global_tracker.write().await;
            global_tracker.add_request(now);
        }

        // 记录来源请求
        {
            let mut source_trackers = self.source_trackers.write().await;
            let tracker = source_trackers.entry(message.source.clone()).or_insert_with(RequestTracker::new);
            tracker.add_request(now);
        }

        // 记录消息类型请求
        {
            let message_type = format!("{:?}", message.message_type).to_lowercase();
            let mut type_trackers = self.message_type_trackers.write().await;
            let tracker = type_trackers.entry(message_type).or_insert_with(RequestTracker::new);
            tracker.add_request(now);
        }

        Ok(())
    }

    /// 获取当前使用情况
    async fn get_current_usage(&self, _source: &str, _message_type: &str) -> RateLimitUsage {
        let global_tracker = self.global_tracker.read().await;
        RateLimitUsage {
            requests_in_window: global_tracker.request_count,
            window_duration: self.config.global_limit.window_duration,
            concurrent_requests: global_tracker.concurrent_count,
        }
    }

    /// 定期清理
    async fn cleanup_if_needed(&self) {
        let mut last_cleanup = self.last_cleanup.write().await;
        let now = Instant::now();
        
        if now.duration_since(*last_cleanup).as_secs() >= self.config.cleanup_interval {
            // 清理过期的黑名单条目
            {
                let mut blacklist = self.blacklist.write().await;
                blacklist.retain(|_, &mut blacklist_until| now < blacklist_until);
            }

            // 清理空的跟踪器
            {
                let mut source_trackers = self.source_trackers.write().await;
                source_trackers.retain(|_, tracker| !tracker.is_empty());
            }

            {
                let mut type_trackers = self.message_type_trackers.write().await;
                type_trackers.retain(|_, tracker| !tracker.is_empty());
            }

            *last_cleanup = now;
            debug!("频率限制器清理完成");
        }
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> RateLimitStats {
        self.stats.read().await.clone()
    }
}

/// 请求跟踪器
#[derive(Debug)]
struct RequestTracker {
    request_timestamps: Vec<Instant>,
    request_count: u32,
    concurrent_count: u32,
}

impl RequestTracker {
    fn new() -> Self {
        Self {
            request_timestamps: Vec::new(),
            request_count: 0,
            concurrent_count: 0,
        }
    }

    fn add_request(&mut self, timestamp: Instant) {
        self.request_timestamps.push(timestamp);
        self.request_count += 1;
        self.concurrent_count += 1;
    }

    fn cleanup_expired_requests(&mut self, now: Instant, window_duration: Duration) {
        let cutoff = now - window_duration;
        self.request_timestamps.retain(|&timestamp| timestamp > cutoff);
        self.request_count = self.request_timestamps.len() as u32;
    }

    fn count_requests_in_window(&self, now: Instant, window_duration: Duration) -> u32 {
        let cutoff = now - window_duration;
        self.request_timestamps.iter()
            .filter(|&&timestamp| timestamp > cutoff)
            .count() as u32
    }

    fn is_empty(&self) -> bool {
        self.request_timestamps.is_empty()
    }
}

/// 频率限制结果
#[derive(Debug, Clone)]
pub struct RateLimitResult {
    /// 是否允许
    pub allowed: bool,
    /// 拒绝原因
    pub reason: String,
    /// 重试等待时间
    pub retry_after: Option<Duration>,
    /// 当前使用情况
    pub current_usage: RateLimitUsage,
}

/// 频率限制使用情况
#[derive(Debug, Clone, Default)]
pub struct RateLimitUsage {
    /// 时间窗口内的请求数
    pub requests_in_window: u32,
    /// 时间窗口持续时间
    pub window_duration: Duration,
    /// 当前并发请求数
    pub concurrent_requests: u32,
}

/// 频率限制统计信息
#[derive(Debug, Clone, Default)]
pub struct RateLimitStats {
    /// 总请求数
    pub total_requests: u64,
    /// 允许的请求数
    pub allowed_requests: u64,
    /// 拒绝的请求数
    pub rejected_requests: u64,
    /// 黑名单条目数
    pub blacklisted_sources: u64,
}
