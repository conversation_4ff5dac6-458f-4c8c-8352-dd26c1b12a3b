//! 加密防护器模块
//!
//! 负责协议层的加密防护，确保消息传输的机密性和安全性

use crate::native_messaging::error::{NativeMessagingError, Result};
use crate::native_messaging::protocol::message::NativeMessage;
use aes_gcm::KeyInit;
use ring::pbkdf2;
use serde::{Deserialize, Serialize};
use std::time::{Duration, SystemTime};
use aes_gcm::{Aes256Gcm, Key, Nonce, aead::Aead};
use rand::{RngCore, thread_rng};
use tracing::{debug, warn};
use std::num::NonZero;

/// 加密防护器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncryptionGuardConfig {
    /// 加密级别
    pub encryption_level: EncryptionLevel,
    /// 启用端到端加密
    pub enable_end_to_end_encryption: bool,
    /// 启用传输层加密
    pub enable_transport_encryption: bool,
    /// 密钥轮换间隔（秒）
    pub key_rotation_interval: u64,
    /// 加密算法
    pub encryption_algorithm: EncryptionAlgorithm,
    /// 密钥派生配置
    pub key_derivation_config: KeyDerivationConfig,
}

impl Default for EncryptionGuardConfig {
    fn default() -> Self {
        Self {
            encryption_level: EncryptionLevel::High,
            enable_end_to_end_encryption: true,
            enable_transport_encryption: true,
            key_rotation_interval: 3600, // 1小时
            encryption_algorithm: EncryptionAlgorithm::Aes256Gcm,
            key_derivation_config: KeyDerivationConfig::default(),
        }
    }
}

/// 加密级别
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum EncryptionLevel {
    /// 无加密
    None,
    /// 低级加密
    Low,
    /// 中级加密
    Medium,
    /// 高级加密
    High,
    /// 军用级加密
    Military,
}

/// 加密算法
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum EncryptionAlgorithm {
    /// AES-256-GCM
    Aes256Gcm,
    /// ChaCha20-Poly1305
    ChaCha20Poly1305,
    /// AES-256-CBC
    Aes256Cbc,
}

/// 密钥派生配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeyDerivationConfig {
    /// 使用PBKDF2
    pub use_pbkdf2: bool,
    /// PBKDF2迭代次数
    pub pbkdf2_iterations: u32,
    /// 盐长度
    pub salt_length: usize,
    /// 主密钥
    pub master_key: Option<String>,
}

impl Default for KeyDerivationConfig {
    fn default() -> Self {
        Self {
            use_pbkdf2: true,
            pbkdf2_iterations: 100000,
            salt_length: 32,
            master_key: None,
        }
    }
}

/// 加密防护器
#[derive(Debug)]
pub struct EncryptionGuard {
    config: EncryptionGuardConfig,
    current_key: Vec<u8>,
    key_generation_time: SystemTime,
    encryption_stats: EncryptionStats,
}

impl EncryptionGuard {
    /// 创建新的加密防护器
    pub fn new(config: EncryptionGuardConfig) -> Result<Self> {
        let current_key = Self::generate_encryption_key(&config)?;
        
        Ok(Self {
            config,
            current_key,
            key_generation_time: SystemTime::now(),
            encryption_stats: EncryptionStats::default(),
        })
    }

    /// 加密消息
    pub async fn encrypt_message(&mut self, message: &NativeMessage) -> Result<EncryptedMessage> {
        debug!("加密消息: {}", message.request_id);

        // 检查是否需要密钥轮换
        self.rotate_key_if_needed().await?;

        match self.config.encryption_level {
            EncryptionLevel::None => {
                // 不加密，直接返回原始消息
                Ok(EncryptedMessage {
                    message_id: message.request_id.clone(),
                    encrypted_data: serde_json::to_vec(message)?,
                    encryption_metadata: EncryptionMetadata {
                        algorithm: self.config.encryption_algorithm.clone(),
                        level: self.config.encryption_level.clone(),
                        nonce: None,
                        key_id: None,
                        encrypted_at: SystemTime::now(),
                    },
                })
            }
            _ => {
                let encrypted_data = self.perform_encryption(message).await?;
                self.encryption_stats.messages_encrypted += 1;
                Ok(encrypted_data)
            }
        }
    }

    /// 解密消息
    pub async fn decrypt_message(&mut self, encrypted_message: &EncryptedMessage) -> Result<NativeMessage> {
        debug!("解密消息: {}", encrypted_message.message_id);

        match encrypted_message.encryption_metadata.level {
            EncryptionLevel::None => {
                // 未加密，直接反序列化
                let message: NativeMessage = serde_json::from_slice(&encrypted_message.encrypted_data)?;
                Ok(message)
            }
            _ => {
                let decrypted_message = self.perform_decryption(encrypted_message).await?;
                self.encryption_stats.messages_decrypted += 1;
                Ok(decrypted_message)
            }
        }
    }

    /// 执行加密
    async fn perform_encryption(&self, message: &NativeMessage) -> Result<EncryptedMessage> {
        let message_bytes = serde_json::to_vec(message)?;

        match self.config.encryption_algorithm {
            EncryptionAlgorithm::Aes256Gcm => {
                self.encrypt_with_aes_gcm(&message_bytes, &message.request_id)
            }
            EncryptionAlgorithm::ChaCha20Poly1305 => {
                // 简化实现，实际应该使用ChaCha20-Poly1305
                self.encrypt_with_aes_gcm(&message_bytes, &message.request_id)
            }
            EncryptionAlgorithm::Aes256Cbc => {
                // 简化实现，实际应该使用AES-256-CBC
                self.encrypt_with_aes_gcm(&message_bytes, &message.request_id)
            }
        }
    }

    /// 使用AES-GCM加密
    fn encrypt_with_aes_gcm(&self, data: &[u8], message_id: &str) -> Result<EncryptedMessage> {
        let key_bytes: &[u8; 32] = self.current_key.as_slice().try_into()
        .map_err(|_| NativeMessagingError::SecurityError("密钥长度不合法，应为32字节".to_string()))?;
        let key = Key::<Aes256Gcm>::from_slice(key_bytes);
        let cipher = Aes256Gcm::new(key);

        // 生成随机nonce
        let mut nonce_bytes = [0u8; 12];
        thread_rng().fill_bytes(&mut nonce_bytes);
        let nonce = Nonce::from_slice(&nonce_bytes);

        // 加密数据
        let encrypted_data = cipher.encrypt(nonce, data)
            .map_err(|e| NativeMessagingError::SecurityError(
                format!("AES-GCM加密失败: {}", e)
            ))?;

        Ok(EncryptedMessage {
            message_id: message_id.to_string(),
            encrypted_data,
            encryption_metadata: EncryptionMetadata {
                algorithm: self.config.encryption_algorithm.clone(),
                level: self.config.encryption_level.clone(),
                nonce: Some(nonce_bytes.to_vec()),
                key_id: Some(self.get_key_id()),
                encrypted_at: SystemTime::now(),
            },
        })
    }

    /// 执行解密
    async fn perform_decryption(&self, encrypted_message: &EncryptedMessage) -> Result<NativeMessage> {
        match encrypted_message.encryption_metadata.algorithm {
            EncryptionAlgorithm::Aes256Gcm => {
                self.decrypt_with_aes_gcm(encrypted_message)
            }
            EncryptionAlgorithm::ChaCha20Poly1305 => {
                // 简化实现
                self.decrypt_with_aes_gcm(encrypted_message)
            }
            EncryptionAlgorithm::Aes256Cbc => {
                // 简化实现
                self.decrypt_with_aes_gcm(encrypted_message)
            }
        }
    }

    /// 使用AES-GCM解密
    fn decrypt_with_aes_gcm(&self, encrypted_message: &EncryptedMessage) -> Result<NativeMessage> {
        let key_bytes: &[u8; 32] = self.current_key.as_slice().try_into()
        .map_err(|_| NativeMessagingError::SecurityError("密钥长度不合法，应为32字节".to_string()))?;
        let key = Key::<Aes256Gcm>::from_slice(key_bytes);
        let cipher = Aes256Gcm::new(key);

        let nonce_bytes = encrypted_message.encryption_metadata.nonce.as_ref()
            .ok_or_else(|| NativeMessagingError::SecurityError(
                "解密需要nonce".to_string()
            ))?;
        let nonce = Nonce::from_slice(nonce_bytes);

        // 解密数据
        let decrypted_data = cipher.decrypt(nonce, encrypted_message.encrypted_data.as_ref())
            .map_err(|e| NativeMessagingError::SecurityError(
                format!("AES-GCM解密失败: {}", e)
            ))?;

        // 反序列化消息
        let message: NativeMessage = serde_json::from_slice(&decrypted_data)?;
        Ok(message)
    }

    /// 生成加密密钥
    fn generate_encryption_key(config: &EncryptionGuardConfig) -> Result<Vec<u8>> {
        let mut key = vec![0u8; 32]; // AES-256需要32字节密钥
        
        if config.key_derivation_config.use_pbkdf2 {
            // 使用PBKDF2派生密钥
            if let Some(ref master_key) = config.key_derivation_config.master_key {
                let mut salt = vec![0u8; config.key_derivation_config.salt_length];
                thread_rng().fill_bytes(&mut salt);
                
                pbkdf2::derive(
                    pbkdf2::PBKDF2_HMAC_SHA256,
                    NonZero::new(config.key_derivation_config.pbkdf2_iterations).unwrap(),
                    &salt,
                    master_key.as_bytes(),
                    &mut key
                );
            } else {
                // 如果没有主密钥，生成随机密钥
                thread_rng().fill_bytes(&mut key);
            }
        } else {
            // 生成随机密钥
            thread_rng().fill_bytes(&mut key);
        }

        Ok(key)
    }

    /// 密钥轮换
    async fn rotate_key_if_needed(&mut self) -> Result<()> {
        let elapsed = SystemTime::now()
            .duration_since(self.key_generation_time)
            .unwrap_or(Duration::ZERO);

        if elapsed.as_secs() >= self.config.key_rotation_interval {
            debug!("执行密钥轮换");
            self.current_key = Self::generate_encryption_key(&self.config)?;
            self.key_generation_time = SystemTime::now();
            self.encryption_stats.key_rotations += 1;
        }

        Ok(())
    }

    /// 获取密钥ID
    fn get_key_id(&self) -> String {
        use sha2::{Sha256, Digest};
        let mut hasher = Sha256::default(); // 改为 default()
        hasher.update(&self.current_key);
        hex::encode(&hasher.finalize()[..8]) // 使用前8字节作为密钥ID
    }

    /// 获取加密统计信息
    pub fn get_encryption_stats(&self) -> &EncryptionStats {
        &self.encryption_stats
    }

    /// 验证加密配置
    pub fn validate_config(&self) -> Result<()> {
        match self.config.encryption_level {
            EncryptionLevel::None => {
                warn!("加密级别设置为None，消息将不被加密");
            }
            EncryptionLevel::Low => {
                warn!("使用低级加密，安全性可能不足");
            }
            _ => {}
        }

        if self.config.key_rotation_interval < 300 {
            warn!("密钥轮换间隔过短，可能影响性能");
        }

        if self.config.key_rotation_interval > 86400 {
            warn!("密钥轮换间隔过长，可能影响安全性");
        }

        Ok(())
    }
}

/// 加密消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncryptedMessage {
    /// 消息ID
    pub message_id: String,
    /// 加密数据
    pub encrypted_data: Vec<u8>,
    /// 加密元数据
    pub encryption_metadata: EncryptionMetadata,
}

/// 加密元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncryptionMetadata {
    /// 加密算法
    pub algorithm: EncryptionAlgorithm,
    /// 加密级别
    pub level: EncryptionLevel,
    /// Nonce/IV
    pub nonce: Option<Vec<u8>>,
    /// 密钥ID
    pub key_id: Option<String>,
    /// 加密时间
    pub encrypted_at: SystemTime,
}

/// 加密统计信息
#[derive(Debug, Default, Clone, Serialize, Deserialize)]
pub struct EncryptionStats {
    /// 已加密消息数
    pub messages_encrypted: u64,
    /// 已解密消息数
    pub messages_decrypted: u64,
    /// 密钥轮换次数
    pub key_rotations: u64,
    /// 加密错误数
    pub encryption_errors: u64,
    /// 解密错误数
    pub decryption_errors: u64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::native_messaging::protocol::message::{MessageType, MessagePriority};
    use std::collections::HashMap;

    fn create_test_message() -> NativeMessage {
        NativeMessage {
            version: 1,
            message_type: MessageType::Test,
            request_id: "test-123".to_string(),
            payload: serde_json::json!({"test": "data"}),
            timestamp: SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs(),
            source: "test-extension".to_string(),
            priority: MessagePriority::Normal,
            timeout_ms: Some(30000),
            metadata: HashMap::new(),
            extensions: HashMap::new(),
        }
    }

    #[tokio::test]
    async fn test_encryption_guard_creation() {
        let config = EncryptionGuardConfig::default();
        let guard = EncryptionGuard::new(config);
        assert!(guard.is_ok());
    }

    #[tokio::test]
    async fn test_message_encryption_decryption() {
        let config = EncryptionGuardConfig::default();
        let mut guard = EncryptionGuard::new(config).unwrap();
        
        let original_message = create_test_message();
        
        // 加密消息
        let encrypted = guard.encrypt_message(&original_message).await.unwrap();
        assert_eq!(encrypted.message_id, original_message.request_id);
        
        // 解密消息
        let decrypted = guard.decrypt_message(&encrypted).await.unwrap();
        assert_eq!(decrypted.request_id, original_message.request_id);
        assert_eq!(decrypted.payload, original_message.payload);
    }

    #[tokio::test]
    async fn test_no_encryption_mode() {
        let mut config = EncryptionGuardConfig::default();
        config.encryption_level = EncryptionLevel::None;
        
        let mut guard = EncryptionGuard::new(config).unwrap();
        let original_message = create_test_message();
        
        let encrypted = guard.encrypt_message(&original_message).await.unwrap();
        let decrypted = guard.decrypt_message(&encrypted).await.unwrap();
        
        assert_eq!(decrypted.request_id, original_message.request_id);
    }
}
