# Native Messaging 安全验证组件 API 文档

## 概述

Native Messaging 安全验证组件提供了一套完整的安全验证功能，用于保护浏览器扩展与本地应用程序之间的通信。该组件包含多个子系统，每个子系统负责特定的安全验证任务。

## 版本信息

- **版本**: 1.0.0
- **API版本**: v1
- **最后更新**: 2024-12-19
- **兼容性**: Rust 1.70+, Tauri 1.5+

## 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    安全验证组件架构                          │
├─────────────────────────────────────────────────────────────┤
│  浏览器身份验证  │  协议防护  │  消息安全  │  威胁检测  │  合规监控  │
│  BrowserAuth    │ ProtocolGuard │ MessageSec │ ThreatDet │ Compliance │
├─────────────────────────────────────────────────────────────┤
│                    核心消息处理层                            │
├─────────────────────────────────────────────────────────────┤
│                    Native Messaging 协议                    │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件

### 1. 浏览器身份验证 (BrowserAuth)

负责验证浏览器扩展的身份和权限。

#### 主要功能
- 扩展ID验证
- 证书链验证
- 来源域名验证
- 白名单管理

#### API 接口

```rust
pub struct BrowserAuthenticator {
    // 内部实现
}

impl BrowserAuthenticator {
    /// 创建新的浏览器认证器
    pub async fn new(config: BrowserAuthConfig) -> Result<Self>;
    
    /// 验证消息的身份
    pub async fn authenticate(&self, message: &NativeMessage) -> Result<AuthResult>;
    
    /// 添加信任的扩展
    pub async fn add_trusted_extension(&mut self, extension_id: &str) -> Result<()>;
    
    /// 移除信任的扩展
    pub async fn remove_trusted_extension(&mut self, extension_id: &str) -> Result<()>;
    
    /// 获取认证统计信息
    pub async fn get_auth_stats(&self) -> AuthStats;
}
```

#### 配置选项

```rust
pub struct BrowserAuthConfig {
    /// 启用扩展验证
    pub enable_extension_verification: bool,
    /// 启用证书验证
    pub enable_certificate_validation: bool,
    /// 启用来源验证
    pub enable_origin_validation: bool,
    /// 启用白名单管理
    pub enable_whitelist_management: bool,
    /// 信任的扩展列表
    pub trusted_extensions: Vec<String>,
    /// 证书存储路径
    pub certificate_store_path: String,
    /// 最大证书链长度
    pub max_certificate_chain_length: usize,
    /// 证书验证超时时间
    pub certificate_validation_timeout: Duration,
}
```

#### 返回结果

```rust
pub struct AuthResult {
    /// 是否通过身份验证
    pub is_authenticated: bool,
    /// 扩展ID
    pub extension_id: Option<String>,
    /// 验证详情
    pub verification_details: Vec<VerificationDetail>,
    /// 信任级别
    pub trust_level: TrustLevel,
    /// 验证时间
    pub verified_at: SystemTime,
}
```

### 2. 协议防护 (ProtocolGuard)

负责验证和保护Native Messaging协议的完整性。

#### 主要功能
- 协议格式验证
- 消息净化
- 版本控制
- 速率限制

#### API 接口

```rust
pub struct ProtocolGuard {
    // 内部实现
}

impl ProtocolGuard {
    /// 创建新的协议防护器
    pub async fn new(config: ProtocolGuardConfig) -> Result<Self>;
    
    /// 验证消息协议
    pub async fn validate_message(&self, message: &NativeMessage) -> Result<ProtocolResult>;
    
    /// 净化消息内容
    pub async fn sanitize_message(&self, message: &NativeMessage) -> Result<NativeMessage>;
    
    /// 检查速率限制
    pub async fn check_rate_limit(&mut self, source: &str) -> Result<RateLimitResult>;
    
    /// 获取协议统计信息
    pub async fn get_protocol_stats(&self) -> ProtocolStats;
}
```

#### 配置选项

```rust
pub struct ProtocolGuardConfig {
    /// 启用协议验证
    pub enable_protocol_validation: bool,
    /// 启用消息净化
    pub enable_message_sanitization: bool,
    /// 启用版本控制
    pub enable_version_control: bool,
    /// 启用速率限制
    pub enable_rate_limiting: bool,
    /// 支持的协议版本
    pub supported_versions: Vec<u32>,
    /// 最大消息大小
    pub max_message_size: usize,
    /// 速率限制配置
    pub rate_limit_config: RateLimitConfig,
    /// 净化规则
    pub sanitization_rules: Vec<SanitizationRule>,
}
```

### 3. 消息安全验证 (MessageSecurity)

负责验证消息的安全性和完整性。

#### 主要功能
- 数字签名验证
- 消息完整性检查
- 重放攻击防护
- 内容过滤

#### API 接口

```rust
pub struct MessageSecurityValidator {
    // 内部实现
}

impl MessageSecurityValidator {
    /// 创建新的消息安全验证器
    pub async fn new(config: MessageSecurityConfig) -> Result<Self>;
    
    /// 验证消息安全性
    pub async fn validate_message(&self, message: &NativeMessage) -> Result<SecurityResult>;
    
    /// 验证消息签名
    pub async fn verify_signature(&self, message: &NativeMessage) -> Result<SignatureResult>;
    
    /// 检查消息完整性
    pub async fn check_integrity(&self, message: &NativeMessage) -> Result<IntegrityResult>;
    
    /// 检查重放攻击
    pub async fn check_replay(&mut self, message: &NativeMessage) -> Result<ReplayResult>;
    
    /// 获取安全统计信息
    pub async fn get_security_stats(&self) -> SecurityStats;
}
```

### 4. 威胁检测 (ThreatDetection)

负责检测和防护各种安全威胁。

#### 主要功能
- 恶意软件检测
- 注入攻击防护
- DoS攻击防护
- 模糊测试防护
- 异常行为检测

#### API 接口

```rust
pub struct ThreatDetector {
    // 内部实现
}

impl ThreatDetector {
    /// 创建新的威胁检测器
    pub async fn new(config: ThreatDetectionConfig) -> Result<Self>;
    
    /// 分析消息威胁
    pub async fn analyze_message(&self, message: &NativeMessage) -> Result<ThreatResult>;
    
    /// 检测恶意软件
    pub async fn detect_malware(&self, message: &NativeMessage) -> Result<MalwareResult>;
    
    /// 检测注入攻击
    pub async fn detect_injection(&self, message: &NativeMessage) -> Result<InjectionResult>;
    
    /// 检测DoS攻击
    pub async fn detect_dos(&mut self, message: &NativeMessage) -> Result<DosResult>;
    
    /// 获取威胁统计信息
    pub async fn get_threat_stats(&self) -> ThreatStats;
}
```

### 5. 合规监控 (ComplianceMonitor)

负责监控系统合规性和生成审计报告。

#### 主要功能
- 策略执行
- 合规检查
- 审计记录
- 安全报告

#### API 接口

```rust
pub struct ComplianceMonitor {
    // 内部实现
}

impl ComplianceMonitor {
    /// 创建新的合规监控器
    pub fn new(config: ComplianceConfig) -> Result<Self>;
    
    /// 监控消息合规性
    pub async fn monitor_compliance(&mut self, message: &NativeMessage) -> Result<ComplianceResult>;
    
    /// 执行策略检查
    pub async fn enforce_policies(&self, message: &NativeMessage) -> Result<PolicyResult>;
    
    /// 记录审计事件
    pub async fn log_audit_event(&mut self, event: &AuditEvent) -> Result<()>;
    
    /// 生成合规报告
    pub async fn generate_report(&mut self, report_type: ReportType) -> Result<SecurityReport>;
    
    /// 获取合规统计信息
    pub async fn get_compliance_stats(&self) -> ComplianceStats;
}
```

## 集成使用示例

### 基本使用

```rust
use crate::native_messaging::security::{
    SecurityValidator, SecurityConfig, ValidationResult
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 1. 创建安全验证器
    let config = SecurityConfig::default();
    let mut validator = SecurityValidator::new(config).await?;
    
    // 2. 验证消息
    let message = create_test_message();
    let result = validator.validate_message(&message).await?;
    
    // 3. 处理验证结果
    if result.is_secure {
        println!("消息验证通过");
        process_secure_message(&message).await?;
    } else {
        println!("消息验证失败: {:?}", result.violations);
        handle_security_violation(&result).await?;
    }
    
    Ok(())
}
```

### 高级配置

```rust
use crate::native_messaging::security::*;

async fn create_custom_validator() -> Result<SecurityValidator, SecurityError> {
    let config = SecurityConfig {
        // 浏览器身份验证配置
        browser_auth: BrowserAuthConfig {
            enable_extension_verification: true,
            enable_certificate_validation: true,
            trusted_extensions: vec![
                "chrome-extension://trusted-id-1".to_string(),
                "chrome-extension://trusted-id-2".to_string(),
            ],
            certificate_validation_timeout: Duration::from_secs(5),
            ..Default::default()
        },
        
        // 协议防护配置
        protocol_guard: ProtocolGuardConfig {
            enable_rate_limiting: true,
            max_message_size: 1024 * 1024, // 1MB
            rate_limit_config: RateLimitConfig {
                max_requests_per_minute: 100,
                burst_size: 10,
                ..Default::default()
            },
            ..Default::default()
        },
        
        // 消息安全配置
        message_security: MessageSecurityConfig {
            enable_signature_verification: true,
            enable_replay_protection: true,
            signature_algorithm: SignatureAlgorithm::HmacSha256,
            replay_window_seconds: 300, // 5分钟
            ..Default::default()
        },
        
        // 威胁检测配置
        threat_detection: ThreatDetectionConfig {
            enable_malware_detection: true,
            enable_injection_detection: true,
            enable_dos_protection: true,
            detection_sensitivity: DetectionSensitivity::High,
            ..Default::default()
        },
        
        // 合规监控配置
        compliance_monitor: ComplianceConfig {
            enable_policy_enforcement: true,
            enable_audit_logging: true,
            supported_standards: vec![
                ComplianceStandard::GDPR,
                ComplianceStandard::SOX,
                ComplianceStandard::HIPAA,
            ],
            ..Default::default()
        },
    };
    
    SecurityValidator::new(config).await
}
```

## 错误处理

### 错误类型

```rust
#[derive(Debug, thiserror::Error)]
pub enum SecurityError {
    #[error("身份验证失败: {0}")]
    AuthenticationFailed(String),
    
    #[error("协议验证失败: {0}")]
    ProtocolValidationFailed(String),
    
    #[error("消息安全验证失败: {0}")]
    MessageSecurityFailed(String),
    
    #[error("威胁检测失败: {0}")]
    ThreatDetectionFailed(String),
    
    #[error("合规检查失败: {0}")]
    ComplianceCheckFailed(String),
    
    #[error("配置错误: {0}")]
    ConfigurationError(String),
    
    #[error("IO错误: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("序列化错误: {0}")]
    SerializationError(#[from] serde_json::Error),
}
```

### 错误处理示例

```rust
match validator.validate_message(&message).await {
    Ok(result) => {
        if result.is_secure {
            // 处理安全消息
        } else {
            // 处理不安全消息
            for violation in &result.violations {
                match violation.violation_type {
                    ViolationType::AuthenticationFailure => {
                        // 处理身份验证失败
                    }
                    ViolationType::ProtocolViolation => {
                        // 处理协议违规
                    }
                    ViolationType::SecurityThreat => {
                        // 处理安全威胁
                    }
                    ViolationType::ComplianceViolation => {
                        // 处理合规违规
                    }
                }
            }
        }
    }
    Err(SecurityError::AuthenticationFailed(msg)) => {
        // 处理身份验证错误
    }
    Err(SecurityError::ConfigurationError(msg)) => {
        // 处理配置错误
    }
    Err(e) => {
        // 处理其他错误
    }
}
```

## 性能指标

### 基准性能

| 组件 | 平均响应时间 | 吞吐量 | 内存使用 |
|------|-------------|--------|----------|
| 浏览器身份验证 | < 2ms | > 500 req/s | < 10MB |
| 协议防护 | < 1ms | > 1000 req/s | < 5MB |
| 消息安全验证 | < 3ms | > 300 req/s | < 15MB |
| 威胁检测 | < 5ms | > 200 req/s | < 20MB |
| 合规监控 | < 2ms | > 500 req/s | < 8MB |

### 检测准确率

| 威胁类型 | 检测率 | 误报率 |
|----------|--------|--------|
| XSS攻击 | > 95% | < 2% |
| SQL注入 | > 98% | < 1% |
| 命令注入 | > 92% | < 3% |
| 恶意软件 | > 90% | < 5% |
| DoS攻击 | > 85% | < 10% |

## 监控和日志

### 日志级别

- **ERROR**: 严重错误，需要立即处理
- **WARN**: 警告信息，可能的安全问题
- **INFO**: 一般信息，正常操作记录
- **DEBUG**: 调试信息，详细的执行过程

### 监控指标

```rust
pub struct SecurityMetrics {
    /// 处理的消息总数
    pub total_messages_processed: u64,
    /// 验证通过的消息数
    pub messages_passed: u64,
    /// 验证失败的消息数
    pub messages_failed: u64,
    /// 检测到的威胁数
    pub threats_detected: u64,
    /// 平均处理时间（毫秒）
    pub avg_processing_time_ms: f64,
    /// 当前内存使用（字节）
    pub current_memory_usage: usize,
}
```

## 部署配置

### 环境变量

```bash
# 日志级别
SECURITY_LOG_LEVEL=info

# 配置文件路径
SECURITY_CONFIG_PATH=/etc/security/config.toml

# 证书存储路径
SECURITY_CERT_STORE=/etc/security/certs

# 审计日志路径
SECURITY_AUDIT_LOG=/var/log/security/audit.log

# 性能监控端口
SECURITY_METRICS_PORT=9090
```

### 配置文件示例

```toml
[browser_auth]
enable_extension_verification = true
enable_certificate_validation = true
trusted_extensions = [
    "chrome-extension://trusted-id-1",
    "chrome-extension://trusted-id-2"
]

[protocol_guard]
enable_rate_limiting = true
max_message_size = 1048576  # 1MB
max_requests_per_minute = 100

[message_security]
enable_signature_verification = true
enable_replay_protection = true
signature_algorithm = "HmacSha256"

[threat_detection]
enable_malware_detection = true
enable_injection_detection = true
detection_sensitivity = "High"

[compliance_monitor]
enable_policy_enforcement = true
enable_audit_logging = true
supported_standards = ["GDPR", "SOX", "HIPAA"]
```

## 故障排除

### 常见问题

1. **身份验证失败**
   - 检查扩展ID是否在白名单中
   - 验证证书是否有效
   - 确认来源域名是否正确

2. **协议验证失败**
   - 检查消息格式是否符合规范
   - 验证协议版本是否支持
   - 确认消息大小是否超限

3. **性能问题**
   - 检查系统资源使用情况
   - 调整配置参数
   - 优化威胁检测规则

### 调试模式

```rust
// 启用详细日志
std::env::set_var("RUST_LOG", "debug");

// 创建调试配置
let mut config = SecurityConfig::default();
config.enable_debug_mode = true;
config.log_all_messages = true;

let validator = SecurityValidator::new(config).await?;
```

## 更新日志

### v1.0.0 (2024-12-19)
- 初始版本发布
- 实现所有核心安全验证功能
- 完成性能优化和测试

### 未来计划
- 支持更多浏览器类型
- 增强机器学习威胁检测
- 添加更多合规标准支持
- 优化性能和内存使用
