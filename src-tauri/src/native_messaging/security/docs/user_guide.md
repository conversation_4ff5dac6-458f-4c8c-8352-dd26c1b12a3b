# Native Messaging 安全验证组件使用指南

## 目录

1. [快速开始](#快速开始)
2. [安装和配置](#安装和配置)
3. [基本使用](#基本使用)
4. [高级配置](#高级配置)
5. [最佳实践](#最佳实践)
6. [故障排除](#故障排除)
7. [性能调优](#性能调优)

## 快速开始

### 5分钟快速体验

```rust
use crate::native_messaging::security::{SecurityValidator, SecurityConfig};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 1. 使用默认配置创建安全验证器
    let validator = SecurityValidator::new(SecurityConfig::default()).await?;
    
    // 2. 创建测试消息
    let message = create_test_message();
    
    // 3. 验证消息
    let result = validator.validate_message(&message).await?;
    
    // 4. 检查结果
    if result.is_secure {
        println!("✅ 消息安全验证通过");
    } else {
        println!("❌ 消息安全验证失败: {:?}", result.violations);
    }
    
    Ok(())
}

fn create_test_message() -> NativeMessage {
    NativeMessage {
        request_id: "test-001".to_string(),
        message_type: MessageType::PasswordGenerate,
        source: "chrome-extension://trusted-extension".to_string(),
        timestamp: SystemTime::now(),
        payload: serde_json::json!({
            "action": "generate_password",
            "length": 16
        }),
        signature: Some("test-signature".to_string()),
    }
}
```

## 安装和配置

### 系统要求

- **操作系统**: Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)
- **Rust版本**: 1.70 或更高
- **内存**: 最少 512MB 可用内存
- **磁盘空间**: 100MB 可用空间

### 依赖项

在 `Cargo.toml` 中添加以下依赖：

```toml
[dependencies]
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tracing = "0.1"
uuid = { version = "1.0", features = ["v4"] }
sha2 = "0.10"
hmac = "0.12"
regex = "1.0"
```

### 基本配置

创建配置文件 `security_config.toml`：

```toml
# 基本安全配置
[general]
enable_debug_logging = false
max_concurrent_validations = 100

# 浏览器身份验证
[browser_auth]
enable_extension_verification = true
enable_certificate_validation = false  # 开发环境可关闭
enable_origin_validation = true
trusted_extensions = [
    "chrome-extension://your-extension-id"
]

# 协议防护
[protocol_guard]
enable_protocol_validation = true
enable_message_sanitization = true
enable_rate_limiting = true
max_message_size = 1048576  # 1MB
max_requests_per_minute = 60

# 消息安全
[message_security]
enable_signature_verification = false  # 开发环境可关闭
enable_integrity_check = true
enable_replay_protection = true
replay_window_seconds = 300

# 威胁检测
[threat_detection]
enable_malware_detection = true
enable_injection_detection = true
enable_dos_protection = true
detection_sensitivity = "Medium"

# 合规监控
[compliance_monitor]
enable_policy_enforcement = true
enable_audit_logging = true
audit_log_path = "./logs/audit.log"
```

## 基本使用

### 1. 创建安全验证器

```rust
use crate::native_messaging::security::*;

// 方式1: 使用默认配置
let validator = SecurityValidator::new(SecurityConfig::default()).await?;

// 方式2: 从配置文件加载
let config = SecurityConfig::from_file("security_config.toml")?;
let validator = SecurityValidator::new(config).await?;

// 方式3: 手动配置
let config = SecurityConfig {
    browser_auth: BrowserAuthConfig {
        enable_extension_verification: true,
        trusted_extensions: vec!["chrome-extension://your-id".to_string()],
        ..Default::default()
    },
    ..Default::default()
};
let validator = SecurityValidator::new(config).await?;
```

### 2. 验证消息

```rust
// 基本验证
let result = validator.validate_message(&message).await?;

if result.is_secure {
    // 消息安全，可以处理
    process_message(&message).await?;
} else {
    // 消息不安全，记录并拒绝
    log_security_violation(&result);
    return Err("消息安全验证失败".into());
}
```

### 3. 处理验证结果

```rust
match validator.validate_message(&message).await {
    Ok(result) => {
        if result.is_secure {
            println!("✅ 消息验证通过");
            println!("   - 安全分数: {:.1}", result.security_score);
            println!("   - 验证时间: {:?}", result.validation_duration);
        } else {
            println!("❌ 消息验证失败");
            for violation in &result.violations {
                println!("   - {}: {}", violation.violation_type, violation.description);
            }
            
            // 根据违规类型采取不同行动
            for action in &result.recommended_actions {
                match action {
                    SecurityAction::Block => {
                        // 阻止消息处理
                        return Err("消息被安全策略阻止".into());
                    }
                    SecurityAction::Monitor => {
                        // 监控但允许通过
                        log_security_warning(&result);
                    }
                    SecurityAction::Alert => {
                        // 发送安全告警
                        send_security_alert(&result).await?;
                    }
                    _ => {}
                }
            }
        }
    }
    Err(e) => {
        eprintln!("验证过程出错: {}", e);
        return Err(e.into());
    }
}
```

## 高级配置

### 1. 自定义威胁检测规则

```rust
let threat_config = ThreatDetectionConfig {
    enable_malware_detection: true,
    enable_injection_detection: true,
    detection_sensitivity: DetectionSensitivity::High,
    custom_malware_signatures: vec![
        MalwareSignature {
            id: "custom_001".to_string(),
            name: "Custom Malware Pattern".to_string(),
            pattern: r"(?i)eval\s*\(\s*atob\s*\(".to_string(),
            signature_type: SignatureType::Regex,
            severity: MalwareSeverity::High,
            description: "检测Base64编码的恶意脚本".to_string(),
            enabled: true,
        }
    ],
    custom_injection_patterns: vec![
        InjectionPattern {
            pattern: r"(?i)union\s+select".to_string(),
            injection_type: InjectionType::SqlInjection,
            severity: InjectionSeverity::High,
            description: "SQL UNION注入检测".to_string(),
        }
    ],
    ..Default::default()
};
```

### 2. 配置合规策略

```rust
let compliance_config = ComplianceConfig {
    enable_policy_enforcement: true,
    enable_compliance_checking: true,
    policy_config: PolicyEngineConfig {
        policy_rules: vec![
            PolicyRule {
                id: "data_size_limit".to_string(),
                name: "数据大小限制".to_string(),
                description: "限制单个消息的数据大小".to_string(),
                condition: PolicyCondition::MessageSizeLimit(512 * 1024), // 512KB
                action: PolicyAction::Block,
                enabled: true,
                priority: 100,
            },
            PolicyRule {
                id: "source_whitelist".to_string(),
                name: "来源白名单".to_string(),
                description: "只允许白名单中的来源".to_string(),
                condition: PolicyCondition::SourceWhitelist(vec![
                    "chrome-extension://trusted-id-1".to_string(),
                    "chrome-extension://trusted-id-2".to_string(),
                ]),
                action: PolicyAction::Allow,
                enabled: true,
                priority: 90,
            }
        ],
        ..Default::default()
    },
    compliance_config: ComplianceCheckerConfig {
        supported_standards: vec![
            ComplianceStandard::GDPR,
            ComplianceStandard::HIPAA,
        ],
        strict_mode: true,
        ..Default::default()
    },
    ..Default::default()
};
```

### 3. 自定义日志和监控

```rust
use tracing::{info, warn, error};
use tracing_subscriber;

// 配置日志
tracing_subscriber::fmt()
    .with_max_level(tracing::Level::INFO)
    .with_target(false)
    .with_thread_ids(true)
    .with_file(true)
    .with_line_number(true)
    .init();

// 自定义事件处理器
struct CustomEventHandler;

impl SecurityEventHandler for CustomEventHandler {
    async fn on_security_violation(&self, violation: &SecurityViolation) {
        match violation.severity {
            ViolationSeverity::Critical => {
                error!("严重安全违规: {}", violation.description);
                // 发送紧急告警
                send_emergency_alert(violation).await;
            }
            ViolationSeverity::High => {
                warn!("高级安全违规: {}", violation.description);
                // 记录到安全日志
                log_to_security_system(violation).await;
            }
            _ => {
                info!("安全事件: {}", violation.description);
            }
        }
    }
    
    async fn on_threat_detected(&self, threat: &ThreatInfo) {
        warn!("检测到威胁: {} - {}", threat.threat_type, threat.description);
        // 更新威胁情报数据库
        update_threat_intelligence(threat).await;
    }
}

// 注册事件处理器
let mut validator = SecurityValidator::new(config).await?;
validator.set_event_handler(Box::new(CustomEventHandler)).await?;
```

## 最佳实践

### 1. 安全配置建议

```rust
// 生产环境推荐配置
let production_config = SecurityConfig {
    browser_auth: BrowserAuthConfig {
        enable_extension_verification: true,
        enable_certificate_validation: true,  // 生产环境必须启用
        enable_origin_validation: true,
        enable_whitelist_management: true,
        certificate_validation_timeout: Duration::from_secs(10),
        ..Default::default()
    },
    
    protocol_guard: ProtocolGuardConfig {
        enable_protocol_validation: true,
        enable_message_sanitization: true,
        enable_rate_limiting: true,
        max_message_size: 256 * 1024,  // 256KB，更严格的限制
        rate_limit_config: RateLimitConfig {
            max_requests_per_minute: 30,  // 更严格的速率限制
            burst_size: 5,
            ..Default::default()
        },
        ..Default::default()
    },
    
    message_security: MessageSecurityConfig {
        enable_signature_verification: true,  // 生产环境必须启用
        enable_integrity_check: true,
        enable_replay_protection: true,
        enable_content_filtering: true,
        signature_algorithm: SignatureAlgorithm::HmacSha256,
        replay_window_seconds: 60,  // 更短的重放窗口
        ..Default::default()
    },
    
    threat_detection: ThreatDetectionConfig {
        enable_malware_detection: true,
        enable_injection_detection: true,
        enable_dos_protection: true,
        enable_fuzzing_detection: true,
        enable_anomaly_detection: true,
        detection_sensitivity: DetectionSensitivity::High,
        ..Default::default()
    },
    
    compliance_monitor: ComplianceConfig {
        enable_policy_enforcement: true,
        enable_compliance_checking: true,
        enable_audit_logging: true,
        enable_security_reporting: true,
        ..Default::default()
    },
};
```

### 2. 错误处理模式

```rust
// 推荐的错误处理模式
async fn handle_message_securely(
    validator: &SecurityValidator,
    message: &NativeMessage
) -> Result<ProcessingResult, ApplicationError> {
    // 1. 安全验证
    let validation_result = match validator.validate_message(message).await {
        Ok(result) => result,
        Err(SecurityError::ConfigurationError(msg)) => {
            // 配置错误，需要修复配置
            error!("安全配置错误: {}", msg);
            return Err(ApplicationError::ConfigurationError(msg));
        }
        Err(SecurityError::AuthenticationFailed(msg)) => {
            // 身份验证失败，拒绝请求
            warn!("身份验证失败: {}", msg);
            return Err(ApplicationError::Unauthorized(msg));
        }
        Err(e) => {
            // 其他安全错误，记录并拒绝
            error!("安全验证失败: {}", e);
            return Err(ApplicationError::SecurityViolation(e.to_string()));
        }
    };
    
    // 2. 检查验证结果
    if !validation_result.is_secure {
        // 记录安全违规
        log_security_violation(&validation_result);
        
        // 根据违规严重程度决定处理方式
        let has_critical_violation = validation_result.violations.iter()
            .any(|v| v.severity == ViolationSeverity::Critical);
            
        if has_critical_violation {
            // 严重违规，立即拒绝
            return Err(ApplicationError::CriticalSecurityViolation);
        } else {
            // 非严重违规，可能允许但需要监控
            warn!("检测到安全问题，但允许继续处理");
        }
    }
    
    // 3. 处理安全的消息
    process_validated_message(message).await
}
```

### 3. 性能优化建议

```rust
// 性能优化配置
let optimized_config = SecurityConfig {
    // 启用缓存以提高性能
    enable_validation_cache: true,
    cache_size: 1000,
    cache_ttl: Duration::from_secs(300),
    
    // 并发处理配置
    max_concurrent_validations: 50,
    validation_timeout: Duration::from_secs(5),
    
    // 异步处理非关键检查
    async_threat_detection: true,
    async_compliance_check: true,
    
    ..Default::default()
};

// 批量验证以提高吞吐量
async fn validate_messages_batch(
    validator: &SecurityValidator,
    messages: Vec<NativeMessage>
) -> Vec<ValidationResult> {
    let futures: Vec<_> = messages.iter()
        .map(|msg| validator.validate_message(msg))
        .collect();
    
    // 并发执行验证
    let results = futures::future::join_all(futures).await;
    
    results.into_iter()
        .map(|r| r.unwrap_or_else(|e| ValidationResult::error(e)))
        .collect()
}
```

## 故障排除

### 常见问题及解决方案

#### 1. 身份验证失败

**问题**: 扩展ID验证失败
```
错误: AuthenticationFailed("扩展ID不在白名单中")
```

**解决方案**:
```rust
// 检查并添加扩展到白名单
let mut validator = SecurityValidator::new(config).await?;
validator.add_trusted_extension("chrome-extension://your-extension-id").await?;

// 或者在配置中添加
let config = SecurityConfig {
    browser_auth: BrowserAuthConfig {
        trusted_extensions: vec![
            "chrome-extension://your-extension-id".to_string()
        ],
        ..Default::default()
    },
    ..Default::default()
};
```

#### 2. 协议验证失败

**问题**: 消息格式不正确
```
错误: ProtocolValidationFailed("消息缺少必需字段")
```

**解决方案**:
```rust
// 确保消息包含所有必需字段
let message = NativeMessage {
    request_id: uuid::Uuid::new_v4().to_string(),  // 必需
    message_type: MessageType::PasswordGenerate,   // 必需
    source: "chrome-extension://valid-id".to_string(), // 必需
    timestamp: SystemTime::now(),                   // 必需
    payload: serde_json::json!({"action": "test"}), // 必需
    signature: Some("valid-signature".to_string()), // 如果启用签名验证则必需
};
```

#### 3. 性能问题

**问题**: 验证速度过慢
```
警告: 消息验证耗时超过5秒
```

**解决方案**:
```rust
// 优化配置以提高性能
let config = SecurityConfig {
    // 禁用非关键检查
    threat_detection: ThreatDetectionConfig {
        enable_malware_detection: false,  // 如果不需要可以禁用
        detection_sensitivity: DetectionSensitivity::Low,
        ..Default::default()
    },
    
    // 减少检查项目
    message_security: MessageSecurityConfig {
        enable_signature_verification: false,  // 开发环境可以禁用
        ..Default::default()
    },
    
    // 启用缓存
    enable_validation_cache: true,
    cache_size: 1000,
    
    ..Default::default()
};
```

### 调试技巧

#### 1. 启用详细日志

```rust
// 设置环境变量
std::env::set_var("RUST_LOG", "debug");
std::env::set_var("SECURITY_DEBUG", "1");

// 或者在代码中配置
use tracing_subscriber;

tracing_subscriber::fmt()
    .with_max_level(tracing::Level::DEBUG)
    .with_target(true)
    .with_thread_ids(true)
    .with_file(true)
    .with_line_number(true)
    .init();
```

#### 2. 使用测试模式

```rust
// 创建测试配置
let test_config = SecurityConfig {
    enable_debug_mode: true,
    log_all_messages: true,
    bypass_security_checks: false,  // 即使在测试模式也不要完全绕过
    
    // 降低安全要求以便测试
    browser_auth: BrowserAuthConfig {
        enable_certificate_validation: false,
        ..Default::default()
    },
    
    message_security: MessageSecurityConfig {
        enable_signature_verification: false,
        ..Default::default()
    },
    
    ..Default::default()
};
```

#### 3. 监控和指标

```rust
// 定期检查性能指标
tokio::spawn(async move {
    let mut interval = tokio::time::interval(Duration::from_secs(60));
    
    loop {
        interval.tick().await;
        
        let metrics = validator.get_metrics().await;
        
        info!("安全验证指标:");
        info!("  - 处理消息数: {}", metrics.total_messages_processed);
        info!("  - 平均处理时间: {:.2}ms", metrics.avg_processing_time_ms);
        info!("  - 检测威胁数: {}", metrics.threats_detected);
        info!("  - 内存使用: {}MB", metrics.current_memory_usage / 1024 / 1024);
        
        // 检查是否有性能问题
        if metrics.avg_processing_time_ms > 10.0 {
            warn!("性能警告: 平均处理时间过长");
        }
        
        if metrics.current_memory_usage > 100 * 1024 * 1024 {  // 100MB
            warn!("内存警告: 内存使用过高");
        }
    }
});
```

## 性能调优

### 1. 缓存策略

```rust
// 配置智能缓存
let config = SecurityConfig {
    enable_validation_cache: true,
    cache_config: CacheConfig {
        max_entries: 10000,
        ttl: Duration::from_secs(300),  // 5分钟
        cleanup_interval: Duration::from_secs(60),  // 1分钟清理一次
        
        // 缓存策略
        cache_successful_validations: true,
        cache_failed_validations: false,  // 不缓存失败的验证
        cache_threat_detections: true,
        
        // LRU策略
        eviction_policy: EvictionPolicy::LRU,
    },
    ..Default::default()
};
```

### 2. 并发优化

```rust
// 配置并发处理
let config = SecurityConfig {
    concurrency_config: ConcurrencyConfig {
        max_concurrent_validations: 100,
        validation_timeout: Duration::from_secs(5),
        
        // 线程池配置
        thread_pool_size: num_cpus::get(),
        queue_size: 1000,
        
        // 背压控制
        enable_backpressure: true,
        backpressure_threshold: 80,  // 80%负载时启用背压
    },
    ..Default::default()
};
```

### 3. 资源管理

```rust
// 定期清理资源
tokio::spawn(async move {
    let mut interval = tokio::time::interval(Duration::from_secs(300));  // 5分钟
    
    loop {
        interval.tick().await;
        
        // 清理缓存
        validator.cleanup_cache().await;
        
        // 清理过期的重放保护记录
        validator.cleanup_replay_protection().await;
        
        // 清理威胁检测历史
        validator.cleanup_threat_history().await;
        
        // 强制垃圾回收（如果需要）
        // std::hint::black_box(vec![0u8; 1024]);  // 触发内存分配
    }
});
```

通过遵循这些最佳实践和调优建议，您可以确保安全验证组件在生产环境中稳定、高效地运行。
