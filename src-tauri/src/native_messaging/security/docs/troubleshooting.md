# Native Messaging 安全验证组件故障排除指南

## 目录

1. [常见错误及解决方案](#常见错误及解决方案)
2. [性能问题诊断](#性能问题诊断)
3. [配置问题](#配置问题)
4. [网络和连接问题](#网络和连接问题)
5. [调试工具和技巧](#调试工具和技巧)
6. [日志分析](#日志分析)
7. [监控和告警](#监控和告警)

## 常见错误及解决方案

### 1. 身份验证错误

#### 错误: `AuthenticationFailed("扩展ID验证失败")`

**症状**:
```
ERROR: 身份验证失败: 扩展ID不在白名单中
Source: chrome-extension://unknown-extension-id
```

**原因**:
- 扩展ID未添加到信任列表
- 扩展ID格式不正确
- 白名单配置错误

**解决方案**:

1. **检查扩展ID格式**:
```rust
// 正确的扩展ID格式
let valid_extension_ids = vec![
    "chrome-extension://abcdefghijklmnopqrstuvwxyz123456",  // Chrome
    "moz-extension://12345678-1234-1234-1234-123456789abc", // Firefox
];
```

2. **添加扩展到白名单**:
```rust
// 方法1: 通过API添加
let mut validator = SecurityValidator::new(config).await?;
validator.add_trusted_extension("chrome-extension://your-extension-id").await?;

// 方法2: 通过配置文件
[browser_auth]
trusted_extensions = [
    "chrome-extension://your-extension-id",
    "moz-extension://your-firefox-extension-id"
]
```

3. **验证扩展ID**:
```bash
# 在浏览器中检查扩展ID
# Chrome: chrome://extensions/
# Firefox: about:debugging#/runtime/this-firefox
```

#### 错误: `AuthenticationFailed("证书验证失败")`

**症状**:
```
ERROR: 证书链验证失败: 证书已过期
Certificate: CN=Extension Certificate
```

**解决方案**:

1. **更新证书**:
```rust
// 检查证书有效期
let cert_info = validator.get_certificate_info("extension-id").await?;
if cert_info.is_expired() {
    warn!("证书已过期，需要更新");
}
```

2. **临时禁用证书验证**（仅开发环境）:
```rust
let config = SecurityConfig {
    browser_auth: BrowserAuthConfig {
        enable_certificate_validation: false,  // 仅开发环境
        ..Default::default()
    },
    ..Default::default()
};
```

### 2. 协议验证错误

#### 错误: `ProtocolValidationFailed("消息格式无效")`

**症状**:
```
ERROR: 协议验证失败: 消息缺少必需字段 'request_id'
Message: {"message_type": "password_generate", "payload": {...}}
```

**解决方案**:

1. **检查消息格式**:
```rust
// 正确的消息格式
let message = NativeMessage {
    request_id: uuid::Uuid::new_v4().to_string(),  // 必需
    message_type: MessageType::PasswordGenerate,   // 必需
    source: "chrome-extension://valid-id".to_string(), // 必需
    timestamp: SystemTime::now(),                   // 必需
    payload: serde_json::json!({                   // 必需
        "action": "generate_password",
        "length": 16
    }),
    signature: Some("signature".to_string()),       // 如果启用签名则必需
};
```

2. **验证消息结构**:
```rust
// 添加消息验证函数
fn validate_message_structure(message: &NativeMessage) -> Result<(), String> {
    if message.request_id.is_empty() {
        return Err("request_id不能为空".to_string());
    }
    
    if message.source.is_empty() {
        return Err("source不能为空".to_string());
    }
    
    if message.payload.is_null() {
        return Err("payload不能为null".to_string());
    }
    
    Ok(())
}
```

#### 错误: `ProtocolValidationFailed("消息大小超限")`

**症状**:
```
ERROR: 消息大小超限: 2MB > 1MB限制
Request ID: large-request-001
```

**解决方案**:

1. **调整大小限制**:
```toml
[protocol_guard]
max_message_size = 2097152  # 2MB
```

2. **压缩大消息**:
```rust
use flate2::Compression;
use flate2::write::GzEncoder;

fn compress_payload(payload: &serde_json::Value) -> Result<Vec<u8>, std::io::Error> {
    let json_str = serde_json::to_string(payload)?;
    let mut encoder = GzEncoder::new(Vec::new(), Compression::default());
    encoder.write_all(json_str.as_bytes())?;
    encoder.finish()
}
```

### 3. 消息安全错误

#### 错误: `MessageSecurityFailed("签名验证失败")`

**症状**:
```
ERROR: 签名验证失败: HMAC不匹配
Expected: a1b2c3d4e5f6...
Actual: f6e5d4c3b2a1...
```

**解决方案**:

1. **检查签名算法**:
```rust
// 确保客户端和服务端使用相同的算法
let config = MessageSecurityConfig {
    signature_algorithm: SignatureAlgorithm::HmacSha256,
    shared_secret: "your-shared-secret".to_string(),
    ..Default::default()
};
```

2. **验证共享密钥**:
```rust
// 生成和验证HMAC签名
use hmac::{Hmac, Mac};
use sha2::Sha256;

type HmacSha256 = Hmac<Sha256>;

fn generate_signature(message: &str, secret: &str) -> String {
    let mut mac = HmacSha256::new_from_slice(secret.as_bytes()).unwrap();
    mac.update(message.as_bytes());
    hex::encode(mac.finalize().into_bytes())
}
```

#### 错误: `MessageSecurityFailed("检测到重放攻击")`

**症状**:
```
WARN: 检测到重放攻击: 消息ID已存在
Request ID: duplicate-request-001
Original timestamp: 2024-12-19 10:30:00
Replay timestamp: 2024-12-19 10:35:00
```

**解决方案**:

1. **确保请求ID唯一**:
```rust
// 使用UUID生成唯一ID
let request_id = format!("{}-{}", 
    uuid::Uuid::new_v4(), 
    SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis()
);
```

2. **调整重放窗口**:
```toml
[message_security]
replay_window_seconds = 600  # 10分钟窗口
```

### 4. 威胁检测错误

#### 错误: `ThreatDetectionFailed("检测到恶意内容")`

**症状**:
```
WARN: 检测到威胁: XSS攻击
Pattern: <script>alert('xss')</script>
Action: 消息被阻止
```

**解决方案**:

1. **净化输入内容**:
```rust
fn sanitize_input(input: &str) -> String {
    input
        .replace("<script>", "&lt;script&gt;")
        .replace("</script>", "&lt;/script&gt;")
        .replace("javascript:", "")
        .replace("data:", "")
}
```

2. **调整检测敏感度**:
```toml
[threat_detection]
detection_sensitivity = "Low"  # 减少误报
enable_content_filtering = false  # 如果不需要内容过滤
```

3. **添加白名单模式**:
```rust
let config = ThreatDetectionConfig {
    whitelist_mode: true,
    trusted_patterns: vec![
        "safe-pattern-1".to_string(),
        "safe-pattern-2".to_string(),
    ],
    ..Default::default()
};
```

## 性能问题诊断

### 1. 响应时间过长

**症状**:
```
WARN: 消息验证耗时过长: 8.5秒 > 5秒阈值
Component: ThreatDetection
Message ID: slow-request-001
```

**诊断步骤**:

1. **启用性能分析**:
```rust
use std::time::Instant;

let start = Instant::now();
let result = validator.validate_message(&message).await?;
let duration = start.elapsed();

if duration.as_millis() > 5000 {
    warn!("验证耗时过长: {:?}", duration);
    
    // 分析各组件耗时
    let breakdown = validator.get_performance_breakdown().await;
    for (component, time) in breakdown {
        info!("{}: {:?}", component, time);
    }
}
```

2. **优化配置**:
```rust
let optimized_config = SecurityConfig {
    // 禁用非关键检查
    threat_detection: ThreatDetectionConfig {
        enable_malware_detection: false,
        enable_fuzzing_detection: false,
        detection_sensitivity: DetectionSensitivity::Low,
        ..Default::default()
    },
    
    // 启用缓存
    enable_validation_cache: true,
    cache_size: 1000,
    
    // 设置超时
    validation_timeout: Duration::from_secs(3),
    
    ..Default::default()
};
```

### 2. 内存使用过高

**症状**:
```
WARN: 内存使用过高: 256MB > 100MB阈值
Active validations: 150
Cache entries: 5000
```

**解决方案**:

1. **监控内存使用**:
```rust
tokio::spawn(async move {
    let mut interval = tokio::time::interval(Duration::from_secs(60));
    
    loop {
        interval.tick().await;
        
        let memory_usage = get_memory_usage();
        if memory_usage > 100 * 1024 * 1024 {  // 100MB
            warn!("内存使用过高: {}MB", memory_usage / 1024 / 1024);
            
            // 清理缓存
            validator.cleanup_cache().await;
            
            // 强制垃圾回收
            std::hint::black_box(vec![0u8; 1024]);
        }
    }
});
```

2. **优化缓存配置**:
```rust
let config = SecurityConfig {
    cache_config: CacheConfig {
        max_entries: 500,  // 减少缓存大小
        ttl: Duration::from_secs(60),  // 缩短TTL
        cleanup_interval: Duration::from_secs(30),  // 更频繁清理
        ..Default::default()
    },
    ..Default::default()
};
```

### 3. CPU使用率过高

**症状**:
```
WARN: CPU使用率过高: 85% > 70%阈值
Active threads: 50
Queue size: 200
```

**解决方案**:

1. **调整并发配置**:
```rust
let config = SecurityConfig {
    concurrency_config: ConcurrencyConfig {
        max_concurrent_validations: 20,  // 减少并发数
        thread_pool_size: 4,  // 限制线程数
        queue_size: 100,  // 减少队列大小
        ..Default::default()
    },
    ..Default::default()
};
```

2. **实现背压控制**:
```rust
async fn validate_with_backpressure(
    validator: &SecurityValidator,
    message: &NativeMessage
) -> Result<ValidationResult, SecurityError> {
    // 检查当前负载
    let current_load = validator.get_current_load().await;
    
    if current_load > 0.8 {  // 80%负载
        // 延迟处理
        tokio::time::sleep(Duration::from_millis(100)).await;
        
        // 或者拒绝请求
        return Err(SecurityError::SystemOverloaded);
    }
    
    validator.validate_message(message).await
}
```

## 配置问题

### 1. 配置文件加载失败

**症状**:
```
ERROR: 无法加载配置文件: /etc/security/config.toml
Cause: No such file or directory
```

**解决方案**:

1. **检查文件路径**:
```bash
# 检查文件是否存在
ls -la /etc/security/config.toml

# 检查权限
ls -la /etc/security/
```

2. **使用默认配置**:
```rust
let config = match SecurityConfig::from_file("config.toml") {
    Ok(config) => config,
    Err(e) => {
        warn!("无法加载配置文件: {}, 使用默认配置", e);
        SecurityConfig::default()
    }
};
```

3. **创建默认配置文件**:
```rust
fn create_default_config_file(path: &str) -> Result<(), std::io::Error> {
    let default_config = SecurityConfig::default();
    let toml_content = toml::to_string(&default_config)?;
    std::fs::write(path, toml_content)?;
    Ok(())
}
```

### 2. 配置验证失败

**症状**:
```
ERROR: 配置验证失败: max_message_size不能为0
Section: protocol_guard
```

**解决方案**:

1. **添加配置验证**:
```rust
impl SecurityConfig {
    pub fn validate(&self) -> Result<(), ConfigError> {
        if self.protocol_guard.max_message_size == 0 {
            return Err(ConfigError::InvalidValue("max_message_size不能为0".to_string()));
        }
        
        if self.browser_auth.trusted_extensions.is_empty() {
            return Err(ConfigError::InvalidValue("trusted_extensions不能为空".to_string()));
        }
        
        Ok(())
    }
}
```

2. **提供配置建议**:
```rust
fn suggest_config_fixes(error: &ConfigError) -> Vec<String> {
    match error {
        ConfigError::InvalidValue(msg) if msg.contains("max_message_size") => {
            vec![
                "设置 max_message_size = 1048576  # 1MB".to_string(),
                "或者 max_message_size = 5242880  # 5MB".to_string(),
            ]
        }
        ConfigError::InvalidValue(msg) if msg.contains("trusted_extensions") => {
            vec![
                "添加至少一个信任的扩展ID:".to_string(),
                "trusted_extensions = [\"chrome-extension://your-id\"]".to_string(),
            ]
        }
        _ => vec!["检查配置文档获取更多信息".to_string()],
    }
}
```

## 网络和连接问题

### 1. 证书下载失败

**症状**:
```
ERROR: 无法下载证书: 连接超时
URL: https://certificates.example.com/cert.pem
Timeout: 10s
```

**解决方案**:

1. **检查网络连接**:
```bash
# 测试网络连接
curl -I https://certificates.example.com/cert.pem

# 检查DNS解析
nslookup certificates.example.com
```

2. **配置代理**:
```rust
let config = SecurityConfig {
    network_config: NetworkConfig {
        proxy_url: Some("http://proxy.company.com:8080".to_string()),
        connect_timeout: Duration::from_secs(30),
        read_timeout: Duration::from_secs(60),
        ..Default::default()
    },
    ..Default::default()
};
```

3. **使用本地证书存储**:
```rust
let config = BrowserAuthConfig {
    enable_certificate_validation: true,
    certificate_store_path: "/etc/ssl/certs".to_string(),
    use_system_cert_store: true,
    ..Default::default()
};
```

## 调试工具和技巧

### 1. 启用详细日志

```rust
// 环境变量配置
std::env::set_var("RUST_LOG", "debug");
std::env::set_var("SECURITY_DEBUG", "1");

// 代码配置
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

tracing_subscriber::registry()
    .with(tracing_subscriber::EnvFilter::new("debug"))
    .with(tracing_subscriber::fmt::layer()
        .with_target(true)
        .with_thread_ids(true)
        .with_file(true)
        .with_line_number(true))
    .init();
```

### 2. 消息跟踪

```rust
// 添加消息跟踪
#[tracing::instrument(skip(validator))]
async fn trace_message_validation(
    validator: &SecurityValidator,
    message: &NativeMessage
) -> Result<ValidationResult, SecurityError> {
    let span = tracing::info_span!("message_validation", 
        request_id = %message.request_id,
        source = %message.source,
        message_type = ?message.message_type
    );
    
    async move {
        tracing::info!("开始验证消息");
        
        let result = validator.validate_message(message).await;
        
        match &result {
            Ok(validation_result) => {
                tracing::info!(
                    is_secure = validation_result.is_secure,
                    security_score = validation_result.security_score,
                    "消息验证完成"
                );
            }
            Err(e) => {
                tracing::error!(error = %e, "消息验证失败");
            }
        }
        
        result
    }.instrument(span).await
}
```

### 3. 性能分析

```rust
use std::collections::HashMap;
use std::time::Instant;

struct PerformanceProfiler {
    timings: HashMap<String, Vec<Duration>>,
}

impl PerformanceProfiler {
    pub fn new() -> Self {
        Self {
            timings: HashMap::new(),
        }
    }
    
    pub async fn profile<F, T>(&mut self, name: &str, operation: F) -> T
    where
        F: std::future::Future<Output = T>,
    {
        let start = Instant::now();
        let result = operation.await;
        let duration = start.elapsed();
        
        self.timings.entry(name.to_string())
            .or_insert_with(Vec::new)
            .push(duration);
        
        result
    }
    
    pub fn report(&self) {
        for (name, durations) in &self.timings {
            let avg = durations.iter().sum::<Duration>() / durations.len() as u32;
            let min = durations.iter().min().unwrap();
            let max = durations.iter().max().unwrap();
            
            println!("{}: avg={:?}, min={:?}, max={:?}, count={}", 
                name, avg, min, max, durations.len());
        }
    }
}
```

## 日志分析

### 1. 日志格式

```
2024-12-19T10:30:00.123Z INFO security::validator [request_id=abc123] 消息验证开始
2024-12-19T10:30:00.125Z DEBUG security::browser_auth [request_id=abc123] 扩展ID验证通过
2024-12-19T10:30:00.127Z WARN security::threat_detection [request_id=abc123] 检测到可疑模式
2024-12-19T10:30:00.130Z INFO security::validator [request_id=abc123] 消息验证完成: secure=true
```

### 2. 日志分析脚本

```bash
#!/bin/bash
# analyze_security_logs.sh

LOG_FILE=${1:-/var/log/security/audit.log}

echo "=== 安全日志分析报告 ==="
echo "日志文件: $LOG_FILE"
echo "分析时间: $(date)"
echo

# 统计验证结果
echo "=== 验证结果统计 ==="
grep "消息验证完成" "$LOG_FILE" | grep -o "secure=[^,]*" | sort | uniq -c

# 统计威胁检测
echo "=== 威胁检测统计 ==="
grep "检测到威胁" "$LOG_FILE" | grep -o "threat_type=[^,]*" | sort | uniq -c

# 统计错误类型
echo "=== 错误类型统计 ==="
grep "ERROR" "$LOG_FILE" | grep -o "error=[^,]*" | sort | uniq -c

# 性能分析
echo "=== 性能分析 ==="
grep "validation_duration" "$LOG_FILE" | awk '{print $NF}' | sort -n | tail -10
```

### 3. 实时监控

```rust
use tokio::fs::File;
use tokio::io::{AsyncBufReadExt, BufReader};

async fn monitor_security_logs(log_path: &str) -> Result<(), std::io::Error> {
    let file = File::open(log_path).await?;
    let reader = BufReader::new(file);
    let mut lines = reader.lines();
    
    while let Some(line) = lines.next_line().await? {
        if line.contains("ERROR") || line.contains("WARN") {
            // 解析日志行
            if let Some(request_id) = extract_request_id(&line) {
                // 发送告警
                send_alert(&format!("安全事件: {} - {}", request_id, line)).await;
            }
        }
        
        if line.contains("检测到威胁") {
            // 威胁检测告警
            send_threat_alert(&line).await;
        }
    }
    
    Ok(())
}

fn extract_request_id(log_line: &str) -> Option<String> {
    // 从日志行中提取request_id
    if let Some(start) = log_line.find("request_id=") {
        let start = start + "request_id=".len();
        if let Some(end) = log_line[start..].find(']') {
            return Some(log_line[start..start + end].to_string());
        }
    }
    None
}
```

## 监控和告警

### 1. 健康检查

```rust
#[derive(Debug, Serialize)]
pub struct HealthStatus {
    pub status: String,
    pub version: String,
    pub uptime: Duration,
    pub components: HashMap<String, ComponentHealth>,
    pub metrics: HealthMetrics,
}

#[derive(Debug, Serialize)]
pub struct ComponentHealth {
    pub status: String,
    pub last_check: SystemTime,
    pub error_count: u64,
    pub response_time: Duration,
}

impl SecurityValidator {
    pub async fn health_check(&self) -> HealthStatus {
        let mut components = HashMap::new();
        
        // 检查各组件健康状态
        components.insert("browser_auth".to_string(), 
            self.check_browser_auth_health().await);
        components.insert("protocol_guard".to_string(), 
            self.check_protocol_guard_health().await);
        components.insert("message_security".to_string(), 
            self.check_message_security_health().await);
        components.insert("threat_detection".to_string(), 
            self.check_threat_detection_health().await);
        components.insert("compliance_monitor".to_string(), 
            self.check_compliance_monitor_health().await);
        
        let overall_status = if components.values().all(|c| c.status == "healthy") {
            "healthy"
        } else {
            "unhealthy"
        };
        
        HealthStatus {
            status: overall_status.to_string(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            uptime: self.get_uptime(),
            components,
            metrics: self.get_health_metrics().await,
        }
    }
}
```

### 2. 告警规则

```rust
#[derive(Debug, Clone)]
pub struct AlertRule {
    pub name: String,
    pub condition: AlertCondition,
    pub severity: AlertSeverity,
    pub cooldown: Duration,
    pub enabled: bool,
}

#[derive(Debug, Clone)]
pub enum AlertCondition {
    ErrorRateExceeds(f64),           // 错误率超过阈值
    ResponseTimeExceeds(Duration),    // 响应时间超过阈值
    ThreatCountExceeds(u64),         // 威胁数量超过阈值
    MemoryUsageExceeds(usize),       // 内存使用超过阈值
    ComponentDown(String),           // 组件不可用
}

async fn check_alert_rules(
    validator: &SecurityValidator,
    rules: &[AlertRule]
) -> Vec<Alert> {
    let mut alerts = Vec::new();
    let metrics = validator.get_metrics().await;
    
    for rule in rules {
        if !rule.enabled {
            continue;
        }
        
        let should_alert = match &rule.condition {
            AlertCondition::ErrorRateExceeds(threshold) => {
                let error_rate = metrics.error_count as f64 / metrics.total_count as f64;
                error_rate > *threshold
            }
            AlertCondition::ResponseTimeExceeds(threshold) => {
                Duration::from_millis(metrics.avg_response_time_ms as u64) > *threshold
            }
            AlertCondition::ThreatCountExceeds(threshold) => {
                metrics.threat_count > *threshold
            }
            AlertCondition::MemoryUsageExceeds(threshold) => {
                metrics.memory_usage > *threshold
            }
            AlertCondition::ComponentDown(component) => {
                let health = validator.health_check().await;
                health.components.get(component)
                    .map(|c| c.status != "healthy")
                    .unwrap_or(true)
            }
        };
        
        if should_alert {
            alerts.push(Alert {
                rule_name: rule.name.clone(),
                severity: rule.severity.clone(),
                message: format!("告警: {}", rule.name),
                timestamp: SystemTime::now(),
                metrics: metrics.clone(),
            });
        }
    }
    
    alerts
}
```

通过遵循这些故障排除指南，您可以快速诊断和解决安全验证组件中的各种问题，确保系统稳定运行。
