# Native Messaging 安全验证组件部署指南

## 目录

1. [部署准备](#部署准备)
2. [环境配置](#环境配置)
3. [安装步骤](#安装步骤)
4. [配置管理](#配置管理)
5. [监控设置](#监控设置)
6. [安全加固](#安全加固)
7. [维护和更新](#维护和更新)

## 部署准备

### 系统要求

#### 最低要求
- **CPU**: 2核心 2.0GHz
- **内存**: 4GB RAM
- **存储**: 10GB 可用空间
- **网络**: 稳定的互联网连接

#### 推荐配置
- **CPU**: 4核心 3.0GHz
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **网络**: 100Mbps 带宽

#### 操作系统支持
- **Linux**: Ubuntu 20.04+, CentOS 8+, RHEL 8+
- **Windows**: Windows 10, Windows Server 2019+
- **macOS**: macOS 11.0+

### 依赖检查

```bash
# 检查Rust版本
rustc --version
# 要求: rustc 1.70.0 或更高

# 检查系统库
ldd --version
pkg-config --version

# 检查网络连接
curl -I https://crates.io
ping -c 4 8.8.8.8
```

### 预部署清单

- [ ] 系统要求满足
- [ ] 网络连接正常
- [ ] 防火墙规则配置
- [ ] SSL证书准备
- [ ] 备份策略制定
- [ ] 监控工具安装
- [ ] 日志轮转配置

## 环境配置

### 1. 开发环境

```bash
# 安装Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# 安装开发依赖
sudo apt-get update
sudo apt-get install -y build-essential pkg-config libssl-dev

# 克隆项目
git clone https://github.com/your-org/secure-password.git
cd secure-password

# 构建项目
cargo build --release
```

### 2. 测试环境

```bash
# 创建测试配置
mkdir -p /etc/security/test
cp configs/test.toml /etc/security/test/config.toml

# 设置环境变量
export SECURITY_ENV=test
export SECURITY_CONFIG_PATH=/etc/security/test/config.toml
export RUST_LOG=debug

# 运行测试
cargo test --release
```

### 3. 生产环境

```bash
# 创建专用用户
sudo useradd -r -s /bin/false security-service
sudo mkdir -p /opt/security-service
sudo chown security-service:security-service /opt/security-service

# 创建配置目录
sudo mkdir -p /etc/security/production
sudo mkdir -p /var/log/security
sudo mkdir -p /var/lib/security

# 设置权限
sudo chown security-service:security-service /var/log/security
sudo chown security-service:security-service /var/lib/security
sudo chmod 750 /etc/security/production
sudo chmod 755 /var/log/security
sudo chmod 750 /var/lib/security
```

## 安装步骤

### 1. 编译和安装

```bash
# 生产环境编译
cargo build --release --target x86_64-unknown-linux-gnu

# 安装二进制文件
sudo cp target/release/secure-password /opt/security-service/
sudo chown security-service:security-service /opt/security-service/secure-password
sudo chmod 755 /opt/security-service/secure-password

# 创建符号链接
sudo ln -sf /opt/security-service/secure-password /usr/local/bin/secure-password
```

### 2. 配置文件安装

```bash
# 复制配置文件
sudo cp configs/production.toml /etc/security/production/config.toml
sudo chown security-service:security-service /etc/security/production/config.toml
sudo chmod 640 /etc/security/production/config.toml

# 复制证书文件
sudo cp certs/* /etc/security/production/certs/
sudo chown -R security-service:security-service /etc/security/production/certs/
sudo chmod 600 /etc/security/production/certs/*
```

### 3. 系统服务配置

#### systemd 服务文件

```ini
# /etc/systemd/system/security-service.service
[Unit]
Description=Native Messaging Security Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=security-service
Group=security-service
ExecStart=/opt/security-service/secure-password
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=security-service

# 环境变量
Environment=SECURITY_ENV=production
Environment=SECURITY_CONFIG_PATH=/etc/security/production/config.toml
Environment=RUST_LOG=info

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/security /var/lib/security

[Install]
WantedBy=multi-user.target
```

#### 启用和启动服务

```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable security-service

# 启动服务
sudo systemctl start security-service

# 检查状态
sudo systemctl status security-service

# 查看日志
sudo journalctl -u security-service -f
```

## 配置管理

### 1. 生产环境配置

```toml
# /etc/security/production/config.toml

[general]
environment = "production"
log_level = "info"
max_concurrent_validations = 100
enable_metrics = true
metrics_port = 9090

[browser_auth]
enable_extension_verification = true
enable_certificate_validation = true
enable_origin_validation = true
enable_whitelist_management = true

trusted_extensions = [
    "chrome-extension://production-extension-id-1",
    "chrome-extension://production-extension-id-2"
]

certificate_store_path = "/etc/security/production/certs"
max_certificate_chain_length = 5
certificate_validation_timeout = "10s"

[protocol_guard]
enable_protocol_validation = true
enable_message_sanitization = true
enable_version_control = true
enable_rate_limiting = true

supported_versions = [1, 2, 3]
max_message_size = 1048576  # 1MB
rate_limit_requests_per_minute = 60
rate_limit_burst_size = 10

[message_security]
enable_signature_verification = true
enable_integrity_check = true
enable_replay_protection = true
enable_content_filtering = true

signature_algorithm = "HmacSha256"
shared_secret_file = "/etc/security/production/secrets/hmac.key"
replay_window_seconds = 300
max_content_filter_rules = 1000

[threat_detection]
enable_malware_detection = true
enable_injection_detection = true
enable_dos_protection = true
enable_fuzzing_detection = true
enable_anomaly_detection = true

detection_sensitivity = "High"
malware_db_path = "/var/lib/security/malware.db"
threat_intel_update_interval = "1h"

[compliance_monitor]
enable_policy_enforcement = true
enable_compliance_checking = true
enable_audit_logging = true
enable_security_reporting = true

supported_standards = ["GDPR", "SOX", "HIPAA", "PCI_DSS"]
audit_log_path = "/var/log/security/audit.log"
report_output_dir = "/var/lib/security/reports"
policy_update_interval = "24h"

[logging]
level = "info"
format = "json"
output = "file"
file_path = "/var/log/security/service.log"
max_file_size = "100MB"
max_files = 10
compress_rotated = true

[monitoring]
enable_health_checks = true
health_check_port = 8080
enable_prometheus_metrics = true
prometheus_port = 9090
enable_jaeger_tracing = false
```

### 2. 密钥管理

```bash
# 生成HMAC密钥
openssl rand -hex 32 > /etc/security/production/secrets/hmac.key
sudo chown security-service:security-service /etc/security/production/secrets/hmac.key
sudo chmod 600 /etc/security/production/secrets/hmac.key

# 生成TLS证书（如果需要）
openssl req -x509 -newkey rsa:4096 -keyout /etc/security/production/certs/server.key \
    -out /etc/security/production/certs/server.crt -days 365 -nodes \
    -subj "/C=US/ST=State/L=City/O=Organization/CN=security-service"

# 设置证书权限
sudo chown security-service:security-service /etc/security/production/certs/server.*
sudo chmod 600 /etc/security/production/certs/server.key
sudo chmod 644 /etc/security/production/certs/server.crt
```

### 3. 环境变量管理

```bash
# 创建环境变量文件
sudo tee /etc/security/production/environment << EOF
SECURITY_ENV=production
SECURITY_CONFIG_PATH=/etc/security/production/config.toml
RUST_LOG=info
RUST_BACKTRACE=1

# 数据库连接
DATABASE_URL=postgresql://security:password@localhost/security_db

# 外部服务
THREAT_INTEL_API_KEY=your-api-key
CERTIFICATE_AUTHORITY_URL=https://ca.example.com

# 监控
PROMETHEUS_GATEWAY=http://prometheus:9091
JAEGER_ENDPOINT=http://jaeger:14268/api/traces
EOF

sudo chown security-service:security-service /etc/security/production/environment
sudo chmod 600 /etc/security/production/environment
```

## 监控设置

### 1. Prometheus 指标

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'security-service'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: /metrics
```

### 2. Grafana 仪表板

```json
{
  "dashboard": {
    "title": "Security Service Metrics",
    "panels": [
      {
        "title": "Message Validation Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(security_messages_validated_total[5m])",
            "legendFormat": "Validations/sec"
          }
        ]
      },
      {
        "title": "Security Score Distribution",
        "type": "histogram",
        "targets": [
          {
            "expr": "security_validation_score_bucket",
            "legendFormat": "Score: {{le}}"
          }
        ]
      },
      {
        "title": "Threat Detection Count",
        "type": "stat",
        "targets": [
          {
            "expr": "security_threats_detected_total",
            "legendFormat": "Total Threats"
          }
        ]
      }
    ]
  }
}
```

### 3. 告警规则

```yaml
# alerts.yml
groups:
  - name: security-service
    rules:
      - alert: HighErrorRate
        expr: rate(security_validation_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate in security service"
          description: "Error rate is {{ $value }} errors per second"

      - alert: ServiceDown
        expr: up{job="security-service"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Security service is down"
          description: "Security service has been down for more than 1 minute"

      - alert: HighMemoryUsage
        expr: security_memory_usage_bytes > 500000000  # 500MB
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value | humanize }}B"

      - alert: ThreatDetectionSpike
        expr: increase(security_threats_detected_total[10m]) > 10
        for: 0m
        labels:
          severity: critical
        annotations:
          summary: "Spike in threat detection"
          description: "{{ $value }} threats detected in the last 10 minutes"
```

### 4. 日志聚合

```yaml
# filebeat.yml
filebeat.inputs:
  - type: log
    enabled: true
    paths:
      - /var/log/security/*.log
    fields:
      service: security-service
      environment: production
    fields_under_root: true
    multiline.pattern: '^\d{4}-\d{2}-\d{2}'
    multiline.negate: true
    multiline.match: after

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "security-logs-%{+yyyy.MM.dd}"

processors:
  - add_host_metadata:
      when.not.contains.tags: forwarded
```

## 安全加固

### 1. 网络安全

```bash
# 防火墙配置
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许必要端口
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 8080/tcp  # 健康检查
sudo ufw allow 9090/tcp  # Prometheus指标

# 限制SSH访问
sudo ufw limit ssh
```

### 2. 文件系统权限

```bash
# 设置严格的文件权限
sudo chmod 750 /opt/security-service
sudo chmod 640 /etc/security/production/config.toml
sudo chmod 600 /etc/security/production/secrets/*
sudo chmod 644 /etc/security/production/certs/*.crt
sudo chmod 600 /etc/security/production/certs/*.key

# 设置SELinux上下文（如果使用SELinux）
sudo setsebool -P httpd_can_network_connect 1
sudo semanage fcontext -a -t bin_t "/opt/security-service/secure-password"
sudo restorecon -v /opt/security-service/secure-password
```

### 3. 系统加固

```bash
# 禁用不必要的服务
sudo systemctl disable bluetooth
sudo systemctl disable cups
sudo systemctl disable avahi-daemon

# 配置自动更新
sudo apt-get install unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades

# 配置fail2ban
sudo apt-get install fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

### 4. 审计配置

```bash
# 配置auditd
sudo apt-get install auditd
sudo systemctl enable auditd

# 添加审计规则
sudo tee -a /etc/audit/rules.d/security-service.rules << EOF
# 监控配置文件变更
-w /etc/security/production/ -p wa -k security-config

# 监控二进制文件
-w /opt/security-service/ -p x -k security-exec

# 监控日志文件
-w /var/log/security/ -p wa -k security-logs

# 监控密钥文件
-w /etc/security/production/secrets/ -p ra -k security-secrets
EOF

sudo systemctl restart auditd
```

## 维护和更新

### 1. 备份策略

```bash
#!/bin/bash
# backup.sh - 备份脚本

BACKUP_DIR="/backup/security-service"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="security-backup-$DATE.tar.gz"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 备份配置和数据
tar -czf "$BACKUP_DIR/$BACKUP_FILE" \
    /etc/security/production/ \
    /var/lib/security/ \
    /opt/security-service/secure-password

# 保留最近30天的备份
find "$BACKUP_DIR" -name "security-backup-*.tar.gz" -mtime +30 -delete

echo "备份完成: $BACKUP_DIR/$BACKUP_FILE"
```

### 2. 更新流程

```bash
#!/bin/bash
# update.sh - 更新脚本

set -e

echo "开始更新安全服务..."

# 1. 备份当前版本
./backup.sh

# 2. 停止服务
sudo systemctl stop security-service

# 3. 备份当前二进制文件
sudo cp /opt/security-service/secure-password /opt/security-service/secure-password.backup

# 4. 部署新版本
sudo cp target/release/secure-password /opt/security-service/
sudo chown security-service:security-service /opt/security-service/secure-password
sudo chmod 755 /opt/security-service/secure-password

# 5. 验证配置
/opt/security-service/secure-password --validate-config

# 6. 启动服务
sudo systemctl start security-service

# 7. 健康检查
sleep 10
curl -f http://localhost:8080/health || {
    echo "健康检查失败，回滚..."
    sudo systemctl stop security-service
    sudo cp /opt/security-service/secure-password.backup /opt/security-service/secure-password
    sudo systemctl start security-service
    exit 1
}

echo "更新完成"
```

### 3. 监控和告警

```bash
#!/bin/bash
# monitor.sh - 监控脚本

# 检查服务状态
if ! systemctl is-active --quiet security-service; then
    echo "CRITICAL: Security service is not running"
    # 发送告警
    curl -X POST "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK" \
        -H 'Content-type: application/json' \
        --data '{"text":"🚨 Security service is down!"}'
    exit 2
fi

# 检查内存使用
MEMORY_USAGE=$(ps -o pid,vsz,rss,comm -p $(pgrep secure-password) | tail -1 | awk '{print $3}')
if [ "$MEMORY_USAGE" -gt 500000 ]; then  # 500MB
    echo "WARNING: High memory usage: ${MEMORY_USAGE}KB"
fi

# 检查日志错误
ERROR_COUNT=$(tail -1000 /var/log/security/service.log | grep -c "ERROR" || true)
if [ "$ERROR_COUNT" -gt 10 ]; then
    echo "WARNING: High error count in logs: $ERROR_COUNT"
fi

echo "OK: All checks passed"
```

### 4. 定期维护任务

```bash
# 添加到crontab
sudo crontab -e

# 每日备份
0 2 * * * /opt/security-service/scripts/backup.sh

# 每周日志轮转
0 3 * * 0 /usr/sbin/logrotate /etc/logrotate.d/security-service

# 每小时监控检查
0 * * * * /opt/security-service/scripts/monitor.sh

# 每月清理临时文件
0 4 1 * * find /tmp -name "security-*" -mtime +7 -delete

# 每周更新威胁情报数据库
0 5 * * 1 /opt/security-service/scripts/update-threat-db.sh
```

通过遵循这个部署指南，您可以安全、可靠地将安全验证组件部署到生产环境中，并确保其长期稳定运行。
