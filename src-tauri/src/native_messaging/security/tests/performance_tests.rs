//! 安全验证组件性能测试
//!
//! 测试各个安全组件的性能指标，确保满足设计要求

use crate::native_messaging::security::{
    browser_auth::{BrowserAuthenticator, BrowserAuthConfig},
    protocol_guard::{ProtocolGuard, ProtocolGuardConfig},
    message_security::{MessageSecurityValidator, MessageSecurityConfig},
    threat_detection::{ThreatDetector, ThreatDetectionConfig},
    compliance_monitor::{ComplianceMonitor, ComplianceConfig},
};
use super::test_utils::*;
use std::time::{Duration, Instant};
use tokio;

#[cfg(test)]
mod performance_tests {
    use super::*;

    /// 测试浏览器身份验证性能
    #[tokio::test]
    async fn test_browser_auth_performance() {
        let authenticator = create_test_browser_authenticator().await;
        let test_message = create_standard_test_message();

        let result = measure_performance(
            || async {
                authenticator.authenticate(&test_message).await
            },
            1000, // 1000次迭代
            5.0,  // 最大5ms平均响应时间
            "浏览器身份验证",
        ).await;

        assert!(result.is_ok(), "浏览器身份验证性能测试失败: {:?}", result);
        println!("✅ 浏览器身份验证性能测试通过");
    }

    /// 测试协议防护性能
    #[tokio::test]
    async fn test_protocol_guard_performance() {
        let protocol_guard = create_test_protocol_guard().await;
        let test_message = create_standard_test_message();

        let result = measure_performance(
            || async {
                protocol_guard.validate_message(&test_message).await
            },
            1000,
            5.0,
            "协议防护",
        ).await;

        assert!(result.is_ok(), "协议防护性能测试失败: {:?}", result);
        println!("✅ 协议防护性能测试通过");
    }

    /// 测试消息安全验证性能
    #[tokio::test]
    async fn test_message_security_performance() {
        let validator = create_test_message_security_validator().await;
        let test_message = create_standard_test_message();

        let result = measure_performance(
            || async {
                validator.validate_message(&test_message).await
            },
            1000,
            5.0,
            "消息安全验证",
        ).await;

        assert!(result.is_ok(), "消息安全验证性能测试失败: {:?}", result);
        println!("✅ 消息安全验证性能测试通过");
    }

    /// 测试威胁检测性能
    #[tokio::test]
    async fn test_threat_detection_performance() {
        let detector = create_test_threat_detector().await;
        let test_message = create_standard_test_message();

        let result = measure_performance(
            || async {
                detector.analyze_message(&test_message).await
            },
            1000,
            5.0,
            "威胁检测",
        ).await;

        assert!(result.is_ok(), "威胁检测性能测试失败: {:?}", result);
        println!("✅ 威胁检测性能测试通过");
    }

    /// 测试合规监控性能
    #[tokio::test]
    async fn test_compliance_monitor_performance() {
        let mut monitor = create_test_compliance_monitor().await;
        let test_message = create_standard_test_message();

        let result = measure_performance(
            || async {
                monitor.monitor_compliance(&test_message).await
            },
            1000,
            5.0,
            "合规监控",
        ).await;

        assert!(result.is_ok(), "合规监控性能测试失败: {:?}", result);
        println!("✅ 合规监控性能测试通过");
    }

    /// 测试大消息处理性能
    #[tokio::test]
    async fn test_large_message_performance() {
        let protocol_guard = create_test_protocol_guard().await;
        let validator = create_test_message_security_validator().await;
        
        // 测试不同大小的消息
        let sizes = vec![1, 10, 100, 1000]; // KB
        
        for size in sizes {
            let large_message = create_large_test_message(size);
            
            // 协议防护性能
            let start = Instant::now();
            let result = protocol_guard.validate_message(&large_message).await;
            let duration = start.elapsed();
            
            assert!(result.is_ok(), "大消息协议验证应该成功");
            assert!(duration.as_millis() < 50, 
                   "{}KB消息协议验证耗时应小于50ms，实际: {}ms", 
                   size, duration.as_millis());
            
            // 消息安全验证性能
            let start = Instant::now();
            let result = validator.validate_message(&large_message).await;
            let duration = start.elapsed();
            
            assert!(result.is_ok(), "大消息安全验证应该成功");
            assert!(duration.as_millis() < 100, 
                   "{}KB消息安全验证耗时应小于100ms，实际: {}ms", 
                   size, duration.as_millis());
        }

        println!("✅ 大消息处理性能测试通过");
    }

    /// 测试并发处理性能
    #[tokio::test]
    async fn test_concurrent_processing_performance() {
        let authenticator = create_test_browser_authenticator().await;
        let test_message = create_standard_test_message();

        let result = test_concurrent_operations(
            || async {
                authenticator.authenticate(&test_message).await
            },
            100, // 100个并发操作
            "浏览器身份验证并发",
        ).await;

        assert!(result.is_ok(), "并发处理性能测试失败: {:?}", result);
        println!("✅ 并发处理性能测试通过");
    }

    /// 测试高频请求处理性能
    #[tokio::test]
    async fn test_high_frequency_performance() {
        let protocol_guard = create_test_protocol_guard().await;
        let messages = create_high_frequency_messages(1000, "test-extension");

        let start = Instant::now();
        let mut success_count = 0;
        let mut error_count = 0;

        for message in messages {
            match protocol_guard.validate_message(&message).await {
                Ok(_) => success_count += 1,
                Err(_) => error_count += 1,
            }
        }

        let total_duration = start.elapsed();
        let avg_ms = total_duration.as_millis() as f64 / 1000.0;
        let throughput = 1000.0 / total_duration.as_secs_f64();

        println!("📊 高频请求处理性能:");
        println!("   - 总耗时: {:?}", total_duration);
        println!("   - 平均耗时: {:.2}ms", avg_ms);
        println!("   - 吞吐量: {:.0} 请求/秒", throughput);
        println!("   - 成功率: {:.1}%", (success_count as f64 / 1000.0) * 100.0);

        assert!(avg_ms < 5.0, "高频请求平均处理时间应小于5ms");
        assert!(throughput > 200.0, "吞吐量应大于200请求/秒");
        assert!(success_count > 950, "成功率应大于95%");

        println!("✅ 高频请求处理性能测试通过");
    }

    /// 测试内存使用性能
    #[tokio::test]
    async fn test_memory_usage_performance() {
        // 测试长时间运行的内存使用情况
        let authenticator = create_test_browser_authenticator().await;
        let protocol_guard = create_test_protocol_guard().await;
        let validator = create_test_message_security_validator().await;
        let detector = create_test_threat_detector().await;
        let mut monitor = create_test_compliance_monitor().await;

        let test_message = create_standard_test_message();
        let iterations = 10000;

        println!("🧠 开始内存使用性能测试 ({} 次迭代)", iterations);

        let start = Instant::now();
        
        for i in 0..iterations {
            // 执行所有安全检查
            let _ = authenticator.authenticate(&test_message).await;
            let _ = protocol_guard.validate_message(&test_message).await;
            let _ = validator.validate_message(&test_message).await;
            let _ = detector.analyze_message(&test_message).await;
            let _ = monitor.monitor_compliance(&test_message).await;

            // 每1000次迭代检查一次
            if i % 1000 == 0 && i > 0 {
                println!("   - 完成 {} 次迭代", i);
                
                // 强制垃圾回收（如果支持）
                // 在Rust中，我们依赖RAII和Drop trait
                
                // 简单的内存压力测试
                let _temp_data: Vec<u8> = vec![0; 1024]; // 1KB临时数据
                drop(_temp_data);
            }
        }

        let total_duration = start.elapsed();
        let avg_ms = total_duration.as_millis() as f64 / iterations as f64;

        println!("📊 内存使用性能测试结果:");
        println!("   - 总耗时: {:?}", total_duration);
        println!("   - 平均耗时: {:.3}ms", avg_ms);
        println!("   - 总迭代: {}", iterations);

        assert!(avg_ms < 1.0, "长时间运行平均处理时间应小于1ms");

        println!("✅ 内存使用性能测试通过");
    }

    /// 测试启动时间性能
    #[tokio::test]
    async fn test_startup_performance() {
        println!("🚀 开始启动时间性能测试");

        // 测试各组件的初始化时间
        let components = vec![
            ("浏览器身份验证", || async {
                let config = BrowserAuthConfig::default();
                BrowserAuthenticator::new(config).await.map(|_| ())
            }),
            ("协议防护", || async {
                let config = ProtocolGuardConfig::default();
                ProtocolGuard::new(config).await.map(|_| ())
            }),
            ("消息安全验证", || async {
                let config = MessageSecurityConfig::default();
                MessageSecurityValidator::new(config).await.map(|_| ())
            }),
            ("威胁检测", || async {
                let config = ThreatDetectionConfig::default();
                ThreatDetector::new(config).await.map(|_| ())
            }),
            ("合规监控", || async {
                let config = ComplianceConfig::default();
                ComplianceMonitor::new(config).map(|_| ())
            }),
        ];

        for (name, init_fn) in components {
            let start = Instant::now();
            let result = init_fn().await;
            let duration = start.elapsed();

            assert!(result.is_ok(), "{}组件初始化应该成功", name);
            assert!(duration.as_millis() < 100, 
                   "{}组件初始化时间应小于100ms，实际: {}ms", 
                   name, duration.as_millis());

            println!("   - {}: {}ms", name, duration.as_millis());
        }

        println!("✅ 启动时间性能测试通过");
    }

    // ========================================================================
    // 辅助函数
    // ========================================================================

    async fn create_test_browser_authenticator() -> BrowserAuthenticator {
        let config = BrowserAuthConfig::default();
        BrowserAuthenticator::new(config).await.expect("创建浏览器认证器失败")
    }

    async fn create_test_protocol_guard() -> ProtocolGuard {
        let config = ProtocolGuardConfig::default();
        ProtocolGuard::new(config).await.expect("创建协议防护器失败")
    }

    async fn create_test_message_security_validator() -> MessageSecurityValidator {
        let config = MessageSecurityConfig::default();
        MessageSecurityValidator::new(config).await.expect("创建消息安全验证器失败")
    }

    async fn create_test_threat_detector() -> ThreatDetector {
        let config = ThreatDetectionConfig::default();
        ThreatDetector::new(config).await.expect("创建威胁检测器失败")
    }

    async fn create_test_compliance_monitor() -> ComplianceMonitor {
        let config = ComplianceConfig::default();
        ComplianceMonitor::new(config).expect("创建合规监控器失败")
    }
}

/// 性能基准测试结果
#[derive(Debug, Clone)]
pub struct PerformanceBenchmark {
    pub component_name: String,
    pub operation_name: String,
    pub iterations: usize,
    pub total_duration: Duration,
    pub avg_duration: Duration,
    pub min_duration: Duration,
    pub max_duration: Duration,
    pub success_rate: f64,
    pub throughput: f64, // 操作/秒
}

impl PerformanceBenchmark {
    pub fn new(
        component_name: String,
        operation_name: String,
        iterations: usize,
        durations: Vec<Duration>,
        success_count: usize,
    ) -> Self {
        let total_duration: Duration = durations.iter().sum();
        let avg_duration = total_duration / iterations as u32;
        let min_duration = durations.iter().min().copied().unwrap_or_default();
        let max_duration = durations.iter().max().copied().unwrap_or_default();
        let success_rate = (success_count as f64 / iterations as f64) * 100.0;
        let throughput = iterations as f64 / total_duration.as_secs_f64();

        Self {
            component_name,
            operation_name,
            iterations,
            total_duration,
            avg_duration,
            min_duration,
            max_duration,
            success_rate,
            throughput,
        }
    }

    pub fn print_report(&self) {
        println!("📊 {} - {} 性能报告:", self.component_name, self.operation_name);
        println!("   - 迭代次数: {}", self.iterations);
        println!("   - 总耗时: {:?}", self.total_duration);
        println!("   - 平均耗时: {:?}", self.avg_duration);
        println!("   - 最小耗时: {:?}", self.min_duration);
        println!("   - 最大耗时: {:?}", self.max_duration);
        println!("   - 成功率: {:.1}%", self.success_rate);
        println!("   - 吞吐量: {:.0} 操作/秒", self.throughput);
    }

    pub fn meets_requirements(&self, max_avg_ms: f64, min_success_rate: f64) -> bool {
        let avg_ms = self.avg_duration.as_millis() as f64;
        avg_ms <= max_avg_ms && self.success_rate >= min_success_rate
    }
}
