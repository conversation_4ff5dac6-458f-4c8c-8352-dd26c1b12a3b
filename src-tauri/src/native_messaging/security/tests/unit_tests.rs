//! 安全验证组件单元测试
//!
//! 提供各个安全组件的详细单元测试

use crate::native_messaging::protocol::message::{NativeMessage, MessageType};
use crate::native_messaging::security::{
    browser_auth::{ExtensionVerifier, WhitelistManager, CertificateChain, OriginValidator},
    protocol_guard::{ProtocolValidator, MessageSanitizer, RateLimiter, EncryptionGuard},
    message_security::{SignatureVerifier, IntegrityChecker, ReplayProtection, ContentFilter},
    threat_detection::{MalwareDetector, InjectionProtection, DosProtection, FuzzingProtection, AnomalyDetector},
    compliance_monitor::{PolicyEngine, AuditLogger, ComplianceChecker, SecurityReporter},
};
use serde_json::json;
use std::time::SystemTime;

#[cfg(test)]
mod browser_auth_tests {
    use super::*;

    #[tokio::test]
    async fn test_extension_verifier() {
        let verifier = ExtensionVerifier::new().await.expect("创建扩展验证器失败");
        
        // 测试有效扩展ID
        let valid_extension_id = "abcdefghijklmnopqrstuvwxyz123456";
        let result = verifier.verify_extension_id(valid_extension_id).await;
        assert!(result.is_ok(), "有效扩展ID验证应该成功");

        // 测试无效扩展ID
        let invalid_extension_id = "invalid-id";
        let result = verifier.verify_extension_id(invalid_extension_id).await;
        assert!(result.is_err() || !result.unwrap().is_valid, "无效扩展ID应该被拒绝");

        println!("✅ 扩展验证器测试通过");
    }

    #[tokio::test]
    async fn test_whitelist_manager() {
        let mut manager = WhitelistManager::new().await.expect("创建白名单管理器失败");
        
        let test_extension = "test-extension-id";
        
        // 测试添加到白名单
        let result = manager.add_to_whitelist(test_extension).await;
        assert!(result.is_ok(), "添加到白名单应该成功");

        // 测试检查白名单
        let is_whitelisted = manager.is_whitelisted(test_extension).await;
        assert!(is_whitelisted.unwrap_or(false), "扩展应该在白名单中");

        // 测试从白名单移除
        let result = manager.remove_from_whitelist(test_extension).await;
        assert!(result.is_ok(), "从白名单移除应该成功");

        let is_whitelisted = manager.is_whitelisted(test_extension).await;
        assert!(!is_whitelisted.unwrap_or(true), "扩展应该不在白名单中");

        println!("✅ 白名单管理器测试通过");
    }

    #[tokio::test]
    async fn test_certificate_chain() {
        let validator = CertificateChain::new().await.expect("创建证书链验证器失败");
        
        // 测试证书链验证（使用模拟数据）
        let mock_cert_chain = vec![
            "-----BEGIN CERTIFICATE-----\nMOCK_CERT_DATA\n-----END CERTIFICATE-----".to_string()
        ];
        
        let result = validator.validate_chain(&mock_cert_chain).await;
        // 由于是模拟数据，预期会失败，但不应该崩溃
        assert!(result.is_ok() || result.is_err(), "证书链验证应该能够处理");

        println!("✅ 证书链验证器测试通过");
    }

    #[tokio::test]
    async fn test_origin_validator() {
        let validator = OriginValidator::new().await.expect("创建来源验证器失败");
        
        // 测试有效来源
        let valid_origins = vec![
            "chrome-extension://abcdefghijklmnopqrstuvwxyz123456",
            "moz-extension://12345678-1234-1234-1234-123456789abc",
        ];
        
        for origin in valid_origins {
            let result = validator.validate_origin(origin).await;
            assert!(result.is_ok(), "有效来源验证应该成功: {}", origin);
        }

        // 测试无效来源
        let invalid_origins = vec![
            "http://malicious-site.com",
            "javascript:alert('xss')",
            "data:text/html,<script>alert('xss')</script>",
        ];
        
        for origin in invalid_origins {
            let result = validator.validate_origin(origin).await;
            assert!(result.is_err() || !result.unwrap().is_valid, 
                   "无效来源应该被拒绝: {}", origin);
        }

        println!("✅ 来源验证器测试通过");
    }
}

#[cfg(test)]
mod protocol_guard_tests {
    use super::*;

    #[tokio::test]
    async fn test_protocol_validator() {
        let validator = ProtocolValidator::new().await.expect("创建协议验证器失败");
        
        let valid_message = create_valid_test_message();
        let result = validator.validate_protocol(&valid_message).await;
        assert!(result.is_ok(), "有效协议消息验证应该成功");
        
        let invalid_message = create_invalid_protocol_message();
        let result = validator.validate_protocol(&invalid_message).await;
        assert!(result.is_err() || !result.unwrap().is_valid, "无效协议消息应该被拒绝");

        println!("✅ 协议验证器测试通过");
    }

    #[tokio::test]
    async fn test_message_sanitizer() {
        let sanitizer = MessageSanitizer::new().await.expect("创建消息净化器失败");
        
        let dirty_message = create_dirty_test_message();
        let result = sanitizer.sanitize_message(&dirty_message).await;
        assert!(result.is_ok(), "消息净化应该成功");
        
        let sanitized = result.unwrap();
        // 验证危险内容已被清理
        let payload_str = serde_json::to_string(&sanitized.payload).unwrap();
        assert!(!payload_str.contains("<script>"), "脚本标签应该被清理");
        assert!(!payload_str.contains("javascript:"), "JavaScript协议应该被清理");

        println!("✅ 消息净化器测试通过");
    }

    #[tokio::test]
    async fn test_rate_limiter() {
        let mut limiter = RateLimiter::new(5, std::time::Duration::from_secs(1))
            .await.expect("创建速率限制器失败");
        
        let source = "test-source";
        
        // 测试正常请求
        for i in 0..5 {
            let result = limiter.check_rate_limit(source).await;
            assert!(result.is_ok() && result.unwrap().is_allowed, 
                   "前5个请求应该被允许: {}", i);
        }
        
        // 测试超出限制的请求
        let result = limiter.check_rate_limit(source).await;
        assert!(result.is_ok() && !result.unwrap().is_allowed, 
               "第6个请求应该被限制");

        println!("✅ 速率限制器测试通过");
    }

    #[tokio::test]
    async fn test_encryption_guard() {
        let guard = EncryptionGuard::new().await.expect("创建加密防护器失败");
        
        let message = create_valid_test_message();
        
        // 测试加密
        let encrypted_result = guard.encrypt_message(&message).await;
        assert!(encrypted_result.is_ok(), "消息加密应该成功");
        
        let encrypted_message = encrypted_result.unwrap();
        
        // 测试解密
        let decrypted_result = guard.decrypt_message(&encrypted_message).await;
        assert!(decrypted_result.is_ok(), "消息解密应该成功");
        
        let decrypted_message = decrypted_result.unwrap();
        assert_eq!(message.request_id, decrypted_message.request_id, 
                  "解密后的消息应该与原消息相同");

        println!("✅ 加密防护器测试通过");
    }
}

#[cfg(test)]
mod message_security_tests {
    use super::*;

    #[tokio::test]
    async fn test_signature_verifier() {
        let verifier = SignatureVerifier::new().await.expect("创建签名验证器失败");
        
        let message = create_signed_test_message();
        let result = verifier.verify_signature(&message).await;
        assert!(result.is_ok(), "签名验证应该成功");

        println!("✅ 签名验证器测试通过");
    }

    #[tokio::test]
    async fn test_integrity_checker() {
        let checker = IntegrityChecker::new().await.expect("创建完整性检查器失败");
        
        let message = create_valid_test_message();
        let result = checker.check_integrity(&message).await;
        assert!(result.is_ok(), "完整性检查应该成功");

        println!("✅ 完整性检查器测试通过");
    }

    #[tokio::test]
    async fn test_replay_protection() {
        let mut protection = ReplayProtection::new().await.expect("创建重放保护器失败");
        
        let message = create_valid_test_message();
        
        // 第一次检查应该通过
        let result = protection.check_replay(&message).await;
        assert!(result.is_ok() && !result.unwrap().is_replay, "首次消息不应该是重放");
        
        // 第二次检查应该检测到重放
        let result = protection.check_replay(&message).await;
        assert!(result.is_ok() && result.unwrap().is_replay, "重复消息应该被检测为重放");

        println!("✅ 重放保护器测试通过");
    }

    #[tokio::test]
    async fn test_content_filter() {
        let filter = ContentFilter::new().await.expect("创建内容过滤器失败");
        
        let malicious_message = create_malicious_content_message();
        let result = filter.filter_content(&malicious_message).await;
        assert!(result.is_ok(), "内容过滤应该成功");
        
        let filter_result = result.unwrap();
        assert!(!filter_result.is_safe, "恶意内容应该被标记为不安全");
        assert!(!filter_result.blocked_content.is_empty(), "应该记录被阻止的内容");

        println!("✅ 内容过滤器测试通过");
    }
}

#[cfg(test)]
mod threat_detection_tests {
    use super::*;

    #[tokio::test]
    async fn test_malware_detector() {
        let detector = MalwareDetector::new().await.expect("创建恶意软件检测器失败");
        
        let suspicious_message = create_suspicious_test_message();
        let result = detector.detect_malware(&suspicious_message).await;
        assert!(result.is_ok(), "恶意软件检测应该成功");

        println!("✅ 恶意软件检测器测试通过");
    }

    #[tokio::test]
    async fn test_injection_protection() {
        let protection = InjectionProtection::new().await.expect("创建注入保护器失败");
        
        let injection_message = create_injection_test_message();
        let result = protection.detect_injection(&injection_message).await;
        assert!(result.is_ok(), "注入检测应该成功");
        
        let detection_result = result.unwrap();
        assert!(detection_result.has_injection, "注入攻击应该被检测到");

        println!("✅ 注入保护器测试通过");
    }

    #[tokio::test]
    async fn test_dos_protection() {
        let mut protection = DosProtection::new().await.expect("创建DoS保护器失败");
        
        let source = "test-source";
        
        // 模拟大量请求
        for _ in 0..100 {
            let _ = protection.check_dos_attack(source).await;
        }
        
        let result = protection.check_dos_attack(source).await;
        assert!(result.is_ok(), "DoS检测应该成功");

        println!("✅ DoS保护器测试通过");
    }
}

// ============================================================================
// 辅助函数
// ============================================================================

fn create_valid_test_message() -> NativeMessage {
    NativeMessage {
        request_id: "test-001".to_string(),
        message_type: MessageType::PasswordGenerate,
        source: "chrome-extension://test".to_string(),
        timestamp: SystemTime::now(),
        payload: json!({"action": "test"}),
        signature: Some("valid-signature".to_string()),
    }
}

fn create_invalid_protocol_message() -> NativeMessage {
    NativeMessage {
        request_id: "".to_string(), // 无效的空ID
        message_type: MessageType::PasswordGenerate,
        source: "invalid-source".to_string(),
        timestamp: SystemTime::UNIX_EPOCH, // 无效时间戳
        payload: json!(null), // 无效载荷
        signature: None,
    }
}

fn create_dirty_test_message() -> NativeMessage {
    NativeMessage {
        request_id: "dirty-001".to_string(),
        message_type: MessageType::PasswordGenerate,
        source: "test-extension".to_string(),
        timestamp: SystemTime::now(),
        payload: json!({
            "script": "<script>alert('xss')</script>",
            "javascript": "javascript:alert('xss')",
            "sql": "'; DROP TABLE users; --"
        }),
        signature: Some("test-signature".to_string()),
    }
}

fn create_signed_test_message() -> NativeMessage {
    NativeMessage {
        request_id: "signed-001".to_string(),
        message_type: MessageType::PasswordGenerate,
        source: "test-extension".to_string(),
        timestamp: SystemTime::now(),
        payload: json!({"action": "test"}),
        signature: Some("valid-test-signature".to_string()),
    }
}

fn create_malicious_content_message() -> NativeMessage {
    NativeMessage {
        request_id: "malicious-001".to_string(),
        message_type: MessageType::PasswordGenerate,
        source: "suspicious-extension".to_string(),
        timestamp: SystemTime::now(),
        payload: json!({
            "virus_signature": "X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*",
            "malicious_url": "http://malware-site.com/payload.exe",
            "suspicious_pattern": "eval(atob('bWFsaWNpb3VzX2NvZGU='))"
        }),
        signature: None,
    }
}

fn create_suspicious_test_message() -> NativeMessage {
    NativeMessage {
        request_id: "suspicious-001".to_string(),
        message_type: MessageType::PasswordGenerate,
        source: "unknown-extension".to_string(),
        timestamp: SystemTime::now(),
        payload: json!({
            "suspicious_behavior": true,
            "unusual_patterns": ["pattern1", "pattern2"]
        }),
        signature: None,
    }
}

fn create_injection_test_message() -> NativeMessage {
    NativeMessage {
        request_id: "injection-001".to_string(),
        message_type: MessageType::PasswordGenerate,
        source: "test-extension".to_string(),
        timestamp: SystemTime::now(),
        payload: json!({
            "sql_injection": "'; DROP TABLE passwords; --",
            "xss_injection": "<script>document.cookie</script>",
            "command_injection": "; rm -rf /"
        }),
        signature: Some("test-signature".to_string()),
    }
}
