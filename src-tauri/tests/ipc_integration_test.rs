//! IPC 集成测试
//! 
//! 测试完整的端到端通信链路：
//! 浏览器扩展 → Native Messaging → Daemon → Tauri 主应用

use secure_password_lib::ipc::{
    IpcMessage, IpcResponse, IpcError, IpcResult,
    DaemonIpcClient, TauriIpcReceiver, MessageRouter
};
use tokio::time::{timeout, Duration};
use std::sync::Arc;
use serde_json::json;

/// 集成测试配置
struct IntegrationTestConfig {
    daemon_address: String,
    test_timeout: Duration,
    max_retries: u32,
}

impl Default for IntegrationTestConfig {
    fn default() -> Self {
        Self {
            daemon_address: "127.0.0.1:8080".to_string(),
            test_timeout: Duration::from_secs(30),
            max_retries: 3,
        }
    }
}

/// 测试工具函数
struct TestUtils;

impl TestUtils {
    /// 等待守护进程启动
    async fn wait_for_daemon(address: &str, max_wait: Duration) -> bool {
        let start = std::time::Instant::now();
        
        while start.elapsed() < max_wait {
            if let Ok(_) = DaemonIpcClient::connect(address).await {
                return true;
            }
            tokio::time::sleep(Duration::from_millis(500)).await;
        }
        
        false
    }
    
    /// 创建测试消息
    fn create_test_message(msg_type: &str, payload: serde_json::Value) -> IpcMessage {
        IpcMessage::new_with_response(msg_type.to_string(), payload)
            .with_source("integration_test".to_string())
            .with_metadata("test_id".to_string(), uuid::Uuid::new_v4().to_string())
    }
    
    /// 验证响应
    fn validate_response(response: &IpcResponse, expected_status: bool) -> bool {
        match expected_status {
            true => response.status == secure_password_lib::ipc::protocol::ResponseStatus::Success,
            false => response.status == secure_password_lib::ipc::protocol::ResponseStatus::Error,
        }
    }
}

/// 基础连接测试
#[tokio::test]
async fn test_daemon_connection() {
    let config = IntegrationTestConfig::default();
    
    // 尝试连接到守护进程
    match DaemonIpcClient::connect(&config.daemon_address).await {
        Ok(client) => {
            println!("✅ 成功连接到守护进程");
            
            // 测试连接状态
            assert!(client.is_connected().await);
            
            // 测试心跳
            let heartbeat_result = client.send_heartbeat().await;
            assert!(heartbeat_result.is_ok(), "心跳测试失败: {:?}", heartbeat_result);
            
            // 关闭连接
            assert!(client.close().await.is_ok());
            println!("✅ 守护进程连接测试通过");
        }
        Err(e) => {
            println!("⚠️  守护进程连接失败: {}", e);
            println!("   请确保守护进程正在运行在 {}", config.daemon_address);
            // 在 CI 环境中，这可能是预期的
        }
    }
}

/// 健康检查测试
#[tokio::test]
async fn test_health_check_flow() {
    let config = IntegrationTestConfig::default();
    
    if !TestUtils::wait_for_daemon(&config.daemon_address, Duration::from_secs(5)).await {
        println!("⚠️  守护进程未运行，跳过健康检查测试");
        return;
    }
    
    let client = match DaemonIpcClient::connect(&config.daemon_address).await {
        Ok(client) => client,
        Err(e) => {
            println!("⚠️  连接守护进程失败: {}", e);
            return;
        }
    };
    
    // 创建健康检查消息
    let health_check = TestUtils::create_test_message(
        "health_check",
        json!({
            "timestamp": chrono::Utc::now().timestamp(),
            "source": "integration_test"
        })
    );
    
    // 发送健康检查请求
    let result = timeout(
        config.test_timeout,
        client.send_request(&health_check)
    ).await;
    
    match result {
        Ok(Ok(response)) => {
            assert!(TestUtils::validate_response(&response, true));
            println!("✅ 健康检查测试通过");
            
            // 验证响应内容
            if let Some(status) = response.payload.get("status") {
                assert_eq!(status.as_str().unwrap_or(""), "healthy");
            }
        }
        Ok(Err(e)) => {
            println!("❌ 健康检查失败: {}", e);
            panic!("健康检查测试失败");
        }
        Err(_) => {
            println!("❌ 健康检查超时");
            panic!("健康检查超时");
        }
    }
    
    let _ = client.close().await;
}

/// 凭证管理测试
#[tokio::test]
async fn test_credential_management_flow() {
    let config = IntegrationTestConfig::default();
    
    if !TestUtils::wait_for_daemon(&config.daemon_address, Duration::from_secs(5)).await {
        println!("⚠️  守护进程未运行，跳过凭证管理测试");
        return;
    }
    
    let client = match DaemonIpcClient::connect(&config.daemon_address).await {
        Ok(client) => client,
        Err(e) => {
            println!("⚠️  连接守护进程失败: {}", e);
            return;
        }
    };
    
    // 测试保存凭证
    let save_credential = TestUtils::create_test_message(
        "save_credential",
        json!({
            "domain": "test.example.com",
            "username": "<EMAIL>",
            "password": "test_password_123",
            "notes": "Integration test credential"
        })
    );
    
    let save_result = timeout(
        config.test_timeout,
        client.send_request(&save_credential)
    ).await;
    
    let credential_id = match save_result {
        Ok(Ok(response)) => {
            if TestUtils::validate_response(&response, true) {
                println!("✅ 凭证保存测试通过");
                response.payload.get("credential_id")
                    .and_then(|v| v.as_str())
                    .unwrap_or("unknown")
                    .to_string()
            } else {
                println!("❌ 凭证保存失败: {:?}", response.error);
                return;
            }
        }
        Ok(Err(e)) => {
            println!("❌ 凭证保存请求失败: {}", e);
            return;
        }
        Err(_) => {
            println!("❌ 凭证保存超时");
            return;
        }
    };
    
    // 测试获取凭证
    let get_credentials = TestUtils::create_test_message(
        "get_credentials",
        json!({
            "domain": "test.example.com"
        })
    );
    
    let get_result = timeout(
        config.test_timeout,
        client.send_request(&get_credentials)
    ).await;
    
    match get_result {
        Ok(Ok(response)) => {
            if TestUtils::validate_response(&response, true) {
                println!("✅ 凭证获取测试通过");
                
                // 验证返回的凭证
                if let Some(credentials) = response.payload.get("credentials") {
                    if let Some(credentials_array) = credentials.as_array() {
                        assert!(!credentials_array.is_empty(), "应该返回至少一个凭证");
                    }
                }
            } else {
                println!("❌ 凭证获取失败: {:?}", response.error);
            }
        }
        Ok(Err(e)) => {
            println!("❌ 凭证获取请求失败: {}", e);
        }
        Err(_) => {
            println!("❌ 凭证获取超时");
        }
    }
    
    // 测试删除凭证
    let delete_credential = TestUtils::create_test_message(
        "delete_credential",
        json!({
            "id": credential_id
        })
    );
    
    let delete_result = timeout(
        config.test_timeout,
        client.send_request(&delete_credential)
    ).await;
    
    match delete_result {
        Ok(Ok(response)) => {
            if TestUtils::validate_response(&response, true) {
                println!("✅ 凭证删除测试通过");
            } else {
                println!("❌ 凭证删除失败: {:?}", response.error);
            }
        }
        Ok(Err(e)) => {
            println!("❌ 凭证删除请求失败: {}", e);
        }
        Err(_) => {
            println!("❌ 凭证删除超时");
        }
    }
    
    let _ = client.close().await;
}

/// 设置管理测试
#[tokio::test]
async fn test_settings_management_flow() {
    let config = IntegrationTestConfig::default();
    
    if !TestUtils::wait_for_daemon(&config.daemon_address, Duration::from_secs(5)).await {
        println!("⚠️  守护进程未运行，跳过设置管理测试");
        return;
    }
    
    let client = match DaemonIpcClient::connect(&config.daemon_address).await {
        Ok(client) => client,
        Err(e) => {
            println!("⚠️  连接守护进程失败: {}", e);
            return;
        }
    };
    
    // 测试获取设置
    let get_settings = TestUtils::create_test_message(
        "get_settings",
        json!({})
    );
    
    let get_result = timeout(
        config.test_timeout,
        client.send_request(&get_settings)
    ).await;
    
    match get_result {
        Ok(Ok(response)) => {
            if TestUtils::validate_response(&response, true) {
                println!("✅ 设置获取测试通过");
                
                // 验证设置结构
                let settings = &response.payload;
                assert!(settings.get("auto_lock_timeout").is_some());
                assert!(settings.get("enable_biometric").is_some());
                assert!(settings.get("sync_enabled").is_some());
                assert!(settings.get("theme").is_some());
            } else {
                println!("❌ 设置获取失败: {:?}", response.error);
            }
        }
        Ok(Err(e)) => {
            println!("❌ 设置获取请求失败: {}", e);
        }
        Err(_) => {
            println!("❌ 设置获取超时");
        }
    }
    
    // 测试更新设置
    let update_settings = TestUtils::create_test_message(
        "update_settings",
        json!({
            "auto_lock_timeout": 300,
            "enable_biometric": false,
            "sync_enabled": true,
            "theme": "dark",
            "language": "zh-CN"
        })
    );
    
    let update_result = timeout(
        config.test_timeout,
        client.send_request(&update_settings)
    ).await;
    
    match update_result {
        Ok(Ok(response)) => {
            if TestUtils::validate_response(&response, true) {
                println!("✅ 设置更新测试通过");
            } else {
                println!("❌ 设置更新失败: {:?}", response.error);
            }
        }
        Ok(Err(e)) => {
            println!("❌ 设置更新请求失败: {}", e);
        }
        Err(_) => {
            println!("❌ 设置更新超时");
        }
    }
    
    let _ = client.close().await;
}

/// 错误处理测试
#[tokio::test]
async fn test_error_handling() {
    let config = IntegrationTestConfig::default();
    
    if !TestUtils::wait_for_daemon(&config.daemon_address, Duration::from_secs(5)).await {
        println!("⚠️  守护进程未运行，跳过错误处理测试");
        return;
    }
    
    let client = match DaemonIpcClient::connect(&config.daemon_address).await {
        Ok(client) => client,
        Err(e) => {
            println!("⚠️  连接守护进程失败: {}", e);
            return;
        }
    };
    
    // 测试不支持的消息类型
    let invalid_message = TestUtils::create_test_message(
        "invalid_message_type",
        json!({})
    );
    
    let result = timeout(
        config.test_timeout,
        client.send_request(&invalid_message)
    ).await;
    
    match result {
        Ok(Ok(response)) => {
            // 应该返回错误响应
            assert!(TestUtils::validate_response(&response, false));
            println!("✅ 无效消息类型错误处理测试通过");
        }
        Ok(Err(e)) => {
            println!("✅ 无效消息类型正确返回错误: {}", e);
        }
        Err(_) => {
            println!("❌ 错误处理测试超时");
        }
    }
    
    // 测试无效的消息格式
    let malformed_message = TestUtils::create_test_message(
        "get_credentials",
        json!({
            "invalid_field": "invalid_value"
            // 缺少必需的 "domain" 字段
        })
    );
    
    let result = timeout(
        config.test_timeout,
        client.send_request(&malformed_message)
    ).await;
    
    match result {
        Ok(Ok(response)) => {
            // 应该返回错误响应
            assert!(TestUtils::validate_response(&response, false));
            println!("✅ 无效消息格式错误处理测试通过");
        }
        Ok(Err(e)) => {
            println!("✅ 无效消息格式正确返回错误: {}", e);
        }
        Err(_) => {
            println!("❌ 错误处理测试超时");
        }
    }
    
    let _ = client.close().await;
}

/// 并发测试
#[tokio::test]
async fn test_concurrent_requests() {
    let config = IntegrationTestConfig::default();
    
    if !TestUtils::wait_for_daemon(&config.daemon_address, Duration::from_secs(5)).await {
        println!("⚠️  守护进程未运行，跳过并发测试");
        return;
    }
    
    let client = Arc::new(match DaemonIpcClient::connect(&config.daemon_address).await {
        Ok(client) => client,
        Err(e) => {
            println!("⚠️  连接守护进程失败: {}", e);
            return;
        }
    });
    
    // 创建多个并发健康检查请求
    let mut tasks = Vec::new();
    
    for i in 0..10 {
        let client_clone = client.clone();
        let task = tokio::spawn(async move {
            let health_check = TestUtils::create_test_message(
                "health_check",
                json!({
                    "timestamp": chrono::Utc::now().timestamp(),
                    "source": format!("concurrent_test_{}", i)
                })
            );
            
            client_clone.send_request(&health_check).await
        });
        
        tasks.push(task);
    }
    
    // 等待所有任务完成
    let results = futures::future::join_all(tasks).await;
    
    let mut success_count = 0;
    let mut error_count = 0;
    
    for result in results {
        match result {
            Ok(Ok(response)) => {
                if TestUtils::validate_response(&response, true) {
                    success_count += 1;
                } else {
                    error_count += 1;
                }
            }
            Ok(Err(_)) => error_count += 1,
            Err(_) => error_count += 1,
        }
    }
    
    println!("并发测试结果: 成功 {}, 失败 {}", success_count, error_count);
    
    // 至少应该有一半的请求成功
    assert!(success_count >= 5, "并发测试成功率过低");
    
    let _ = client.close().await;
    println!("✅ 并发测试通过");
}
