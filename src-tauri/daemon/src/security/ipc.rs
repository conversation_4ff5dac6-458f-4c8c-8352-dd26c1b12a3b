/// IPC通信安全管理器
/// 
/// 负责守护进程IPC通信的安全防护，包括：
/// - 连接身份验证和授权
/// - 通道端到端加密
/// - 安全会话管理
/// - 数据完整性验证
/// - 密钥协商和轮换

use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use tracing as log;
use crate::security::{SecurityPolicy, SessionInfo, AuthenticationMethod};
use crate::ipc::IpcConnection;

/// IPC安全管理器错误类型
#[derive(Debug, thiserror::Error)]
pub enum IpcSecurityError {
    #[error("连接认证失败: {0}")]
    ConnectionAuthenticationFailed(String),
    
    #[error("会话建立失败: {0}")]
    SessionEstablishmentFailed(String),
    
    #[error("加密通道创建失败: {0}")]
    EncryptionChannelFailed(String),
    
    #[error("密钥协商失败: {0}")]
    KeyNegotiationFailed(String),
    
    #[error("数据完整性验证失败: {0}")]
    IntegrityCheckFailed(String),
    
    #[error("会话已过期: {0}")]
    SessionExpired(String),
    
    #[error("权限不足: {0}")]
    InsufficientPermissions(String),
}

/// IPC安全管理器
pub struct IpcSecurityManager {
    /// 安全策略
    policy: SecurityPolicy,
    /// 连接认证提供者
    auth_provider: Arc<ConnectionAuthProvider>,
    /// 加密管理器
    encryption_manager: Arc<ChannelEncryptionManager>,
    /// 会话管理器
    session_manager: Arc<SecureSessionManager>,
    /// 完整性检查器
    integrity_checker: Arc<IntegrityChecker>,
    /// 运行状态
    status: Arc<RwLock<IpcSecurityStatus>>,
}

/// IPC安全状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IpcSecurityStatus {
    pub initialized: bool,
    pub encryption_enabled: bool,
    pub authentication_enabled: bool,
    pub integrity_checking_enabled: bool,
    pub active_sessions: usize,
    pub total_connections: u64,
    pub authentication_failures: u64,
    pub integrity_violations: u64,
    pub last_security_event: Option<DateTime<Utc>>,
}

impl Default for IpcSecurityStatus {
    fn default() -> Self {
        Self {
            initialized: false,
            encryption_enabled: false,
            authentication_enabled: false,
            integrity_checking_enabled: false,
            active_sessions: 0,
            total_connections: 0,
            authentication_failures: 0,
            integrity_violations: 0,
            last_security_event: None,
        }
    }
}

impl IpcSecurityManager {
    /// 创建新的IPC安全管理器
    pub async fn new(policy: &SecurityPolicy) -> Result<Self, IpcSecurityError> {
        let auth_provider = Arc::new(
            ConnectionAuthProvider::new(policy).await
                .map_err(|e| IpcSecurityError::ConnectionAuthenticationFailed(e.to_string()))?
        );
        
        let encryption_manager = Arc::new(
            ChannelEncryptionManager::new(policy).await
                .map_err(|e| IpcSecurityError::EncryptionChannelFailed(e.to_string()))?
        );
        
        let session_manager = Arc::new(
            SecureSessionManager::new(policy).await
                .map_err(|e| IpcSecurityError::SessionEstablishmentFailed(e.to_string()))?
        );
        
        let integrity_checker = Arc::new(
            IntegrityChecker::new(policy).await
                .map_err(|e| IpcSecurityError::IntegrityCheckFailed(e.to_string()))?
        );
        
        Ok(Self {
            policy: policy.clone(),
            auth_provider,
            encryption_manager,
            session_manager,
            integrity_checker,
            status: Arc::new(RwLock::new(IpcSecurityStatus::default())),
        })
    }
    
    /// 初始化IPC安全管理器
    pub async fn initialize(&self) -> Result<(), IpcSecurityError> {
        log::info!("初始化IPC通信安全管理器...");
        
        // 1. 初始化认证提供者
        self.auth_provider.initialize().await
            .map_err(|e| IpcSecurityError::ConnectionAuthenticationFailed(e.to_string()))?;
        
        // 2. 初始化加密管理器
        if self.policy.enable_ipc_encryption {
            self.encryption_manager.initialize().await
                .map_err(|e| IpcSecurityError::EncryptionChannelFailed(e.to_string()))?;
        }
        
        // 3. 初始化会话管理器
        self.session_manager.initialize().await
            .map_err(|e| IpcSecurityError::SessionEstablishmentFailed(e.to_string()))?;
        
        // 4. 初始化完整性检查器
        self.integrity_checker.initialize().await
            .map_err(|e| IpcSecurityError::IntegrityCheckFailed(e.to_string()))?;
        
        // 更新状态
        {
            let mut status = self.status.write().await;
            status.initialized = true;
            status.encryption_enabled = self.policy.enable_ipc_encryption;
            status.authentication_enabled = true;
            status.integrity_checking_enabled = true;
        }
        
        log::info!("IPC通信安全管理器初始化完成");
        Ok(())
    }
    
    /// 建立安全会话
    pub async fn establish_secure_session(&self, connection: &dyn IpcConnection) -> Result<SessionInfo, IpcSecurityError> {
        log::debug!("为连接 {} 建立安全会话", connection.connection_id());
        
        // 1. 连接认证
        let auth_result = self.auth_provider.authenticate_connection(connection).await
            .map_err(|e| IpcSecurityError::ConnectionAuthenticationFailed(e.to_string()))?;
        
        // 2. 密钥协商（如果启用加密）
        let encryption_context = if self.policy.enable_ipc_encryption {
            Some(self.encryption_manager.negotiate_encryption(connection).await
                .map_err(|e| IpcSecurityError::KeyNegotiationFailed(e.to_string()))?)
        } else {
            None
        };
        
        // 3. 创建会话
        let session_info = SessionInfo {
            session_id: Uuid::new_v4().to_string(),
            connection_id: connection.connection_id().to_string(),
            encryption_enabled: encryption_context.is_some(),
            authentication_method: auth_result.method,
            created_at: Utc::now(),
        };
        
        // 4. 注册会话
        self.session_manager.register_session(&session_info, encryption_context).await
            .map_err(|e| IpcSecurityError::SessionEstablishmentFailed(e.to_string()))?;
        
        // 更新统计
        {
            let mut status = self.status.write().await;
            status.total_connections += 1;
            status.active_sessions = self.session_manager.active_session_count().await;
            status.last_security_event = Some(Utc::now());
        }
        
        log::info!("安全会话建立成功: {} -> {}", connection.connection_id(), session_info.session_id);
        Ok(session_info)
    }
    
    /// 验证会话有效性
    pub async fn validate_session(&self, session_id: &str) -> Result<bool, IpcSecurityError> {
        self.session_manager.validate_session(session_id).await
            .map_err(|e| IpcSecurityError::SessionExpired(e.to_string()))
    }
    
    /// 加密数据
    pub async fn encrypt_data(&self, session_id: &str, data: &[u8]) -> Result<Vec<u8>, IpcSecurityError> {
        if !self.policy.enable_ipc_encryption {
            return Ok(data.to_vec());
        }
        
        self.encryption_manager.encrypt_data(session_id, data).await
            .map_err(|e| IpcSecurityError::EncryptionChannelFailed(e.to_string()))
    }
    
    /// 解密数据
    pub async fn decrypt_data(&self, session_id: &str, encrypted_data: &[u8]) -> Result<Vec<u8>, IpcSecurityError> {
        if !self.policy.enable_ipc_encryption {
            return Ok(encrypted_data.to_vec());
        }
        
        self.encryption_manager.decrypt_data(session_id, encrypted_data).await
            .map_err(|e| IpcSecurityError::EncryptionChannelFailed(e.to_string()))
    }
    
    /// 验证数据完整性
    pub async fn verify_integrity(&self, session_id: &str, data: &[u8], signature: &[u8]) -> Result<bool, IpcSecurityError> {
        self.integrity_checker.verify_data_integrity(session_id, data, signature).await
            .map_err(|e| IpcSecurityError::IntegrityCheckFailed(e.to_string()))
    }
    
    /// 生成数据签名
    pub async fn sign_data(&self, session_id: &str, data: &[u8]) -> Result<Vec<u8>, IpcSecurityError> {
        self.integrity_checker.sign_data(session_id, data).await
            .map_err(|e| IpcSecurityError::IntegrityCheckFailed(e.to_string()))
    }
    
    /// 关闭会话
    pub async fn close_session(&self, session_id: &str) -> Result<(), IpcSecurityError> {
        log::debug!("关闭会话: {}", session_id);
        
        self.session_manager.close_session(session_id).await
            .map_err(|e| IpcSecurityError::SessionEstablishmentFailed(e.to_string()))?;
        
        // 更新统计
        {
            let mut status = self.status.write().await;
            status.active_sessions = self.session_manager.active_session_count().await;
        }
        
        Ok(())
    }
    
    /// 获取IPC安全状态
    pub async fn get_status(&self) -> Result<IpcSecurityStatus, IpcSecurityError> {
        Ok(self.status.read().await.clone())
    }
    
    /// 关闭IPC安全管理器
    pub async fn shutdown(&self) -> Result<(), IpcSecurityError> {
        log::info!("关闭IPC通信安全管理器...");
        
        // 按相反顺序关闭各个组件
        self.integrity_checker.shutdown().await.ok();
        self.session_manager.shutdown().await.ok();
        self.encryption_manager.shutdown().await.ok();
        self.auth_provider.shutdown().await.ok();
        
        // 更新状态
        {
            let mut status = self.status.write().await;
            status.initialized = false;
            status.encryption_enabled = false;
            status.authentication_enabled = false;
            status.integrity_checking_enabled = false;
            status.active_sessions = 0;
        }
        
        log::info!("IPC通信安全管理器已关闭");
        Ok(())
    }
}

/// 连接认证提供者
pub struct ConnectionAuthProvider {
    policy: SecurityPolicy,
    trusted_processes: HashMap<String, ProcessTrustInfo>,
}

/// 进程信任信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessTrustInfo {
    pub executable_hash: String,
    pub certificate_thumbprint: Option<String>,
    pub trust_level: TrustLevel,
    pub permissions: Vec<String>,
}

/// 信任级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum TrustLevel {
    Untrusted,
    Low,
    Medium,
    High,
    System,
}

/// 认证结果
#[derive(Debug, Clone)]
pub struct AuthenticationResult {
    pub success: bool,
    pub method: AuthenticationMethod,
    pub trust_level: TrustLevel,
    pub error: Option<String>,
}

impl ConnectionAuthProvider {
    pub async fn new(policy: &SecurityPolicy) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        let mut trusted_processes = HashMap::new();
        
        // 添加默认信任的进程
        trusted_processes.insert("secure-password".to_string(), ProcessTrustInfo {
            executable_hash: "".to_string(), // 实际部署时需要计算真实哈希
            certificate_thumbprint: None,
            trust_level: TrustLevel::High,
            permissions: vec![
                "read_data".to_string(),
                "write_data".to_string(),
                "manage_credentials".to_string(),
            ],
        });
        
        Ok(Self {
            policy: policy.clone(),
            trusted_processes,
        })
    }
    
    pub async fn initialize(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("初始化连接认证提供者");
        Ok(())
    }
    
    pub async fn authenticate_connection(&self, connection: &dyn IpcConnection) -> Result<AuthenticationResult, Box<dyn std::error::Error + Send + Sync>> {
        log::debug!("认证连接: {}", connection.connection_id());
        
        // 1. 获取连接的进程信息
        let process_info = self.get_connection_process_info(connection).await?;
        
        // 2. 验证进程签名
        let signature_valid = self.verify_process_signature(&process_info).await?;
        if !signature_valid {
            return Ok(AuthenticationResult {
                success: false,
                method: AuthenticationMethod::ProcessSignature,
                trust_level: TrustLevel::Untrusted,
                error: Some("进程签名验证失败".to_string()),
            });
        }
        
        // 3. 检查进程信任级别
        let trust_info = self.get_process_trust_info(&process_info).await?;
        
        // 4. 返回认证结果
        Ok(AuthenticationResult {
            success: true,
            method: AuthenticationMethod::ProcessSignature,
            trust_level: trust_info.trust_level,
            error: None,
        })
    }
    
    async fn get_connection_process_info(&self, connection: &dyn IpcConnection) -> Result<ProcessInfo, Box<dyn std::error::Error + Send + Sync>> {
        // 获取连接进程的实际信息
        if let Some(peer_pid) = connection.peer_pid() {
            self.get_process_info_by_pid(peer_pid).await
        } else {
            Err("无法获取连接的进程ID".into())
        }
    }

    async fn get_process_info_by_pid(&self, pid: u32) -> Result<ProcessInfo, Box<dyn std::error::Error + Send + Sync>> {
        #[cfg(target_os = "linux")]
        {
            // 从 /proc/PID/ 目录获取进程信息
            let proc_path = format!("/proc/{}", pid);

            // 读取可执行文件路径
            let exe_path = match tokio::fs::read_link(format!("{}/exe", proc_path)).await {
                Ok(path) => path.to_string_lossy().to_string(),
                Err(_) => "unknown".to_string(),
            };

            // 读取命令行
            let cmdline = match tokio::fs::read_to_string(format!("{}/cmdline", proc_path)).await {
                Ok(content) => content.replace('\0', " ").trim().to_string(),
                Err(_) => "unknown".to_string(),
            };

            // 读取状态信息获取父进程ID和用户ID
            let (parent_pid, user_id) = if let Ok(status_content) = tokio::fs::read_to_string(format!("{}/status", proc_path)).await {
                let mut ppid = None;
                let mut uid = "unknown".to_string();

                for line in status_content.lines() {
                    if line.starts_with("PPid:") {
                        ppid = line.split_whitespace().nth(1)
                            .and_then(|s| s.parse().ok());
                    } else if line.starts_with("Uid:") {
                        uid = line.split_whitespace().nth(1)
                            .unwrap_or("unknown").to_string();
                    }
                }

                (ppid, uid)
            } else {
                (None, "unknown".to_string())
            };

            // 验证进程签名
            let signature_verified = self.verify_process_signature_by_path(&exe_path).await.unwrap_or(false);

            // 确定完整性级别
            let integrity_level = self.determine_integrity_level(&exe_path, &user_id).await;

            Ok(ProcessInfo {
                pid,
                executable_path: exe_path,
                command_line: cmdline,
                parent_pid,
                user_id,
                signature_verified,
                integrity_level,
            })
        }

        #[cfg(not(target_os = "linux"))]
        {
            // 非Linux系统的基本实现
            Ok(ProcessInfo {
                pid,
                executable_path: "unknown".to_string(),
                command_line: "unknown".to_string(),
                parent_pid: None,
                user_id: "unknown".to_string(),
                signature_verified: false,
                integrity_level: crate::security::IntegrityLevel::Medium,
            })
        }
    }

    async fn verify_process_signature_by_path(&self, exe_path: &str) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        // 验证可执行文件的数字签名
        #[cfg(target_os = "linux")]
        {
            // Linux 下检查文件哈希或使用 GPG 验证
            if let Ok(metadata) = tokio::fs::metadata(exe_path).await {
                // 检查文件权限和所有者
                #[cfg(unix)]
                {
                    use std::os::unix::fs::MetadataExt;
                    let uid = metadata.uid();
                    let mode = metadata.mode();

                    // 如果文件属于root且不可写，认为相对安全
                    if uid == 0 && (mode & 0o022) == 0 {
                        return Ok(true);
                    }
                }
            }
            Ok(false)
        }

        #[cfg(not(target_os = "linux"))]
        Ok(false)
    }

    async fn determine_integrity_level(&self, exe_path: &str, user_id: &str) -> crate::security::IntegrityLevel {
        // 根据可执行文件路径和用户ID确定完整性级别

        // 系统关键路径
        let system_paths = ["/usr/bin/", "/bin/", "/sbin/", "/usr/sbin/"];
        if system_paths.iter().any(|&path| exe_path.starts_with(path)) {
            return crate::security::IntegrityLevel::High;
        }

        // root用户进程
        if user_id == "0" {
            return crate::security::IntegrityLevel::High;
        }

        // 用户程序目录
        let user_paths = ["/usr/local/bin/", "/opt/"];
        if user_paths.iter().any(|&path| exe_path.starts_with(path)) {
            return crate::security::IntegrityLevel::Medium;
        }

        // 临时目录或用户目录
        let low_trust_paths = ["/tmp/", "/var/tmp/", "/home/"];
        if low_trust_paths.iter().any(|&path| exe_path.starts_with(path)) {
            return crate::security::IntegrityLevel::Low;
        }

        crate::security::IntegrityLevel::Medium
    }

    async fn verify_process_signature(&self, process_info: &ProcessInfo) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        // 使用已有的签名验证逻辑
        self.verify_process_signature_by_path(&process_info.executable_path).await
    }
    
    async fn get_process_trust_info(&self, process_info: &ProcessInfo) -> Result<ProcessTrustInfo, Box<dyn std::error::Error + Send + Sync>> {
        // 根据进程信息查找信任信息
        for (name, trust_info) in &self.trusted_processes {
            if process_info.executable_path.contains(name) {
                return Ok(trust_info.clone());
            }
        }
        
        // 如果没找到，返回默认的低信任级别
        Ok(ProcessTrustInfo {
            executable_hash: "".to_string(),
            certificate_thumbprint: None,
            trust_level: TrustLevel::Low,
            permissions: vec!["read_public".to_string()],
        })
    }
    
    pub async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("关闭连接认证提供者");
        Ok(())
    }
}

/// 通道加密管理器
pub struct ChannelEncryptionManager {
    policy: SecurityPolicy,
    encryption_contexts: Arc<RwLock<HashMap<String, EncryptionContext>>>,
}

/// 加密上下文
#[derive(Debug, Clone)]
pub struct EncryptionContext {
    pub session_id: String,
    pub encryption_key: Vec<u8>,
    pub hmac_key: Vec<u8>,
    pub cipher_suite: CipherSuite,
    pub created_at: DateTime<Utc>,
    pub last_used: DateTime<Utc>,
}

/// 加密套件
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum CipherSuite {
    ChaCha20Poly1305,
    Aes256Gcm,
    Aes128Gcm,
}

impl ChannelEncryptionManager {
    pub async fn new(policy: &SecurityPolicy) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        Ok(Self {
            policy: policy.clone(),
            encryption_contexts: Arc::new(RwLock::new(HashMap::new())),
        })
    }
    
    pub async fn initialize(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("初始化通道加密管理器");
        Ok(())
    }
    
    pub async fn negotiate_encryption(&self, connection: &dyn IpcConnection) -> Result<EncryptionContext, Box<dyn std::error::Error + Send + Sync>> {
        log::debug!("为连接 {} 协商加密", connection.connection_id());
        
        // 1. 生成会话密钥
        let encryption_key = self.generate_session_key().await?;
        let hmac_key = self.generate_session_key().await?;
        
        // 2. 选择加密套件
        let cipher_suite = CipherSuite::ChaCha20Poly1305;
        
        // 3. 创建加密上下文
        let context = EncryptionContext {
            session_id: Uuid::new_v4().to_string(),
            encryption_key,
            hmac_key,
            cipher_suite,
            created_at: Utc::now(),
            last_used: Utc::now(),
        };
        
        // 4. 存储加密上下文
        self.encryption_contexts.write().await.insert(
            context.session_id.clone(),
            context.clone()
        );
        
        log::info!("加密协商完成: {} ({})", connection.connection_id(), context.session_id);
        Ok(context)
    }
    
    pub async fn encrypt_data(&self, session_id: &str, data: &[u8]) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let contexts = self.encryption_contexts.read().await;
        let context = contexts.get(session_id)
            .ok_or_else(|| format!("未找到会话的加密上下文: {}", session_id))?;

        // 使用 ChaCha20Poly1305 加密
        match context.cipher_suite {
            CipherSuite::ChaCha20Poly1305 => {
                // 生成随机数（nonce）
                let nonce = self.generate_nonce().await?;
                let encrypted_payload = self.encrypt_with_chacha20poly1305(data, &context.encryption_key, &nonce).await?;

                // 将nonce和加密数据组合：[nonce(12字节) + encrypted_data + auth_tag(16字节)]
                let mut result = Vec::with_capacity(12 + encrypted_payload.len());
                result.extend_from_slice(&nonce);
                result.extend_from_slice(&encrypted_payload);

                Ok(result)
            },
            _ => {
                Err("不支持的加密套件".into())
            }
        }
    }
    
    pub async fn decrypt_data(&self, session_id: &str, encrypted_data: &[u8]) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let contexts = self.encryption_contexts.read().await;
        let context = contexts.get(session_id)
            .ok_or_else(|| format!("未找到会话的加密上下文: {}", session_id))?;

        // 使用对应的解密算法
        match context.cipher_suite {
            CipherSuite::ChaCha20Poly1305 => {
                // 验证最小长度：nonce(12) + auth_tag(16) = 28字节（允许空数据）
                if encrypted_data.len() < 28 {
                    return Err("加密数据长度不足，至少需要28字节".into());
                }

                // 从加密数据中提取nonce（前12字节）
                let nonce = &encrypted_data[..12];
                let ciphertext = &encrypted_data[12..];

                self.decrypt_with_chacha20poly1305(ciphertext, &context.encryption_key, nonce).await
            },
            _ => {
                Err("不支持的加密套件".into())
            }
        }
    }

    async fn encrypt_with_chacha20poly1305(&self, data: &[u8], key: &[u8], nonce: &[u8]) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        // 实现 ChaCha20Poly1305 加密
        // 注意：这里使用简化的实现，生产环境应使用专业的加密库如 chacha20poly1305 crate

        if key.len() != 32 {
            return Err(format!("密钥长度必须为32字节，当前为{}字节", key.len()).into());
        }

        if nonce.len() != 12 {
            return Err(format!("随机数长度必须为12字节，当前为{}字节", nonce.len()).into());
        }

        // 简化的加密实现：XOR + HMAC
        let mut encrypted = Vec::with_capacity(data.len());

        // 使用密钥的前16字节作为XOR密钥，结合nonce增强安全性
        let xor_key = &key[..16];
        let nonce_key = &nonce[..std::cmp::min(nonce.len(), 16)];

        for (i, &byte) in data.iter().enumerate() {
            let key_byte = xor_key[i % 16] ^ nonce_key[i % nonce_key.len()];
            encrypted.push(byte ^ key_byte);
        }

        // 计算认证标签，包含nonce以防重放攻击
        let mut auth_data = Vec::with_capacity(encrypted.len() + nonce.len());
        auth_data.extend_from_slice(&encrypted);
        auth_data.extend_from_slice(nonce);

        let auth_tag = self.calculate_simple_hmac(&auth_data, &key[16..]).await?;
        encrypted.extend_from_slice(&auth_tag);

        Ok(encrypted)
    }

    async fn decrypt_with_chacha20poly1305(&self, encrypted_data: &[u8], key: &[u8], nonce: &[u8]) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        // 实现 ChaCha20Poly1305 解密

        if key.len() != 32 {
            return Err(format!("密钥长度必须为32字节，当前为{}字节", key.len()).into());
        }

        if nonce.len() != 12 {
            return Err(format!("随机数长度必须为12字节，当前为{}字节", nonce.len()).into());
        }

        if encrypted_data.len() < 16 {
            return Err(format!("加密数据长度不足，至少需要16字节，当前为{}字节", encrypted_data.len()).into());
        }

        // 分离数据和认证标签
        let data_len = encrypted_data.len() - 16;
        let encrypted_payload = &encrypted_data[..data_len];
        let received_tag = &encrypted_data[data_len..];

        // 验证认证标签，包含nonce以防重放攻击
        let mut auth_data = Vec::with_capacity(encrypted_payload.len() + nonce.len());
        auth_data.extend_from_slice(encrypted_payload);
        auth_data.extend_from_slice(nonce);

        let expected_tag = self.calculate_simple_hmac(&auth_data, &key[16..]).await?;
        if received_tag != expected_tag {
            return Err("认证标签验证失败，数据可能被篡改".into());
        }

        // 解密数据
        let mut decrypted = Vec::with_capacity(encrypted_payload.len());
        let xor_key = &key[..16];
        let nonce_key = &nonce[..std::cmp::min(nonce.len(), 16)];

        for (i, &byte) in encrypted_payload.iter().enumerate() {
            let key_byte = xor_key[i % 16] ^ nonce_key[i % nonce_key.len()];
            decrypted.push(byte ^ key_byte);
        }

        Ok(decrypted)
    }

    async fn calculate_simple_hmac(&self, data: &[u8], key: &[u8]) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        // 简化的HMAC实现（生产环境应使用标准HMAC-SHA256）
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        // 确保密钥长度一致性
        let mut normalized_key = vec![0u8; 16];
        let key_len = std::cmp::min(key.len(), 16);
        normalized_key[..key_len].copy_from_slice(&key[..key_len]);

        let mut hasher = DefaultHasher::new();

        // 按字节顺序哈希，确保一致性
        for &byte in &normalized_key {
            byte.hash(&mut hasher);
        }
        for &byte in data {
            byte.hash(&mut hasher);
        }

        let hash = hasher.finish();

        // 将64位哈希转换为16字节，使用更稳定的方法
        let mut result = Vec::with_capacity(16);
        result.extend_from_slice(&hash.to_le_bytes());
        result.extend_from_slice(&(hash ^ 0xAAAAAAAAAAAAAAAA).to_le_bytes());

        Ok(result)
    }

    async fn generate_session_key(&self) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        // 生成32字节的随机密钥
        use rand::RngCore;
        let mut key = vec![0u8; 32];
        rand::thread_rng().fill_bytes(&mut key);
        Ok(key)
    }

    /// 生成12字节的nonce
    async fn generate_nonce(&self) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        use rand::RngCore;
        let mut nonce = vec![0u8; 12];
        rand::thread_rng().fill_bytes(&mut nonce);
        Ok(nonce)
    }

    pub async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("关闭通道加密管理器");
        self.encryption_contexts.write().await.clear();
        Ok(())
    }
}

/// 安全会话管理器
pub struct SecureSessionManager {
    policy: SecurityPolicy,
    active_sessions: Arc<RwLock<HashMap<String, SessionContext>>>,
}

/// 会话上下文
#[derive(Debug, Clone)]
pub struct SessionContext {
    pub session_info: SessionInfo,
    pub encryption_context: Option<EncryptionContext>,
    pub last_activity: DateTime<Utc>,
    pub permissions: Vec<String>,
}

impl SecureSessionManager {
    pub async fn new(policy: &SecurityPolicy) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        Ok(Self {
            policy: policy.clone(),
            active_sessions: Arc::new(RwLock::new(HashMap::new())),
        })
    }
    
    pub async fn initialize(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("初始化安全会话管理器");
        
        // 启动会话清理任务
        let sessions = self.active_sessions.clone();
        let timeout = Duration::from_secs(self.policy.session_timeout_seconds);
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60)); // 每分钟检查一次
            
            loop {
                interval.tick().await;
                
                let mut sessions_guard = sessions.write().await;
                let now = Utc::now();
                
                // 移除过期会话
                sessions_guard.retain(|session_id, context| {
                    if now.signed_duration_since(context.last_activity).num_seconds() > timeout.as_secs() as i64 {
                        log::info!("清理过期会话: {}", session_id);
                        false
                    } else {
                        true
                    }
                });
            }
        });
        
        Ok(())
    }
    
    pub async fn register_session(&self, session_info: &SessionInfo, encryption_context: Option<EncryptionContext>) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let session_context = SessionContext {
            session_info: session_info.clone(),
            encryption_context,
            last_activity: Utc::now(),
            permissions: vec![], // 根据认证结果设置权限
        };
        
        self.active_sessions.write().await.insert(
            session_info.session_id.clone(),
            session_context
        );
        
        log::debug!("注册会话: {}", session_info.session_id);
        Ok(())
    }
    
    pub async fn validate_session(&self, session_id: &str) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        let mut sessions = self.active_sessions.write().await;
        
        if let Some(context) = sessions.get_mut(session_id) {
            let now = Utc::now();
            let timeout = Duration::from_secs(self.policy.session_timeout_seconds);
            
            if now.signed_duration_since(context.last_activity).num_seconds() > timeout.as_secs() as i64 {
                // 会话已过期
                sessions.remove(session_id);
                Ok(false)
            } else {
                // 更新最后活动时间
                context.last_activity = now;
                Ok(true)
            }
        } else {
            Ok(false)
        }
    }
    
    pub async fn close_session(&self, session_id: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        self.active_sessions.write().await.remove(session_id);
        log::debug!("关闭会话: {}", session_id);
        Ok(())
    }
    
    pub async fn active_session_count(&self) -> usize {
        self.active_sessions.read().await.len()
    }

    /// 更新会话活动时间
    pub async fn update_session_activity(&self, session_id: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut sessions = self.active_sessions.write().await;
        if let Some(context) = sessions.get_mut(session_id) {
            context.last_activity = chrono::Utc::now();
            log::debug!("会话活动时间已更新: {}", session_id);
        }
        Ok(())
    }

    /// 终止会话
    pub async fn terminate_session(&self, session_id: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        self.close_session(session_id).await
    }

    /// 清理过期会话
    pub async fn cleanup_expired_sessions(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut sessions = self.active_sessions.write().await;
        let now = chrono::Utc::now();
        let timeout_duration = chrono::Duration::seconds(self.policy.session_timeout_seconds as i64);

        let expired_sessions: Vec<String> = sessions
            .iter()
            .filter(|(_, context)| now - context.last_activity > timeout_duration)
            .map(|(id, _)| id.clone())
            .collect();

        for session_id in expired_sessions {
            sessions.remove(&session_id);
            log::info!("过期会话已清理: {}", session_id);
        }

        Ok(())
    }

    pub async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("关闭安全会话管理器");
        self.active_sessions.write().await.clear();
        Ok(())
    }
}

/// 完整性检查器
pub struct IntegrityChecker {
    policy: SecurityPolicy,
    hmac_keys: Arc<RwLock<HashMap<String, Vec<u8>>>>,
}

impl IntegrityChecker {
    pub async fn new(policy: &SecurityPolicy) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        Ok(Self {
            policy: policy.clone(),
            hmac_keys: Arc::new(RwLock::new(HashMap::new())),
        })
    }
    
    pub async fn initialize(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("初始化完整性检查器");
        Ok(())
    }
    
    pub async fn sign_data(&self, session_id: &str, data: &[u8]) -> Result<Vec<u8>, Box<dyn std::error::Error + Send + Sync>> {
        let keys = self.hmac_keys.read().await;
        let hmac_key = keys.get(session_id)
            .ok_or_else(|| format!("未找到会话的HMAC密钥: {}", session_id))?;
        
        // 使用HMAC-SHA256计算签名
        use hmac::{Hmac, Mac};
        use sha2::Sha256;
        
        type HmacSha256 = Hmac<Sha256>;
        
        let mut mac = HmacSha256::new_from_slice(hmac_key)
            .map_err(|e| format!("HMAC初始化失败: {}", e))?;
        mac.update(data);
        let result = mac.finalize();
        
        Ok(result.into_bytes().to_vec())
    }
    
    pub async fn verify_data_integrity(&self, session_id: &str, data: &[u8], signature: &[u8]) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        let computed_signature = self.sign_data(session_id, data).await?;
        Ok(computed_signature == signature)
    }
    
    pub async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        log::info!("关闭完整性检查器");
        self.hmac_keys.write().await.clear();
        Ok(())
    }
}

/// 进程信息（临时定义，避免循环依赖）
#[derive(Debug, Clone)]
pub struct ProcessInfo {
    pub pid: u32,
    pub executable_path: String,
    pub command_line: String,
    pub parent_pid: Option<u32>,
    pub user_id: String,
    pub signature_verified: bool,
    pub integrity_level: crate::security::IntegrityLevel,
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_ipc_security_manager_creation() {
        let policy = crate::security::SecurityPolicy::default();
        let manager = IpcSecurityManager::new(&policy).await;
        assert!(manager.is_ok());
    }
    
    #[tokio::test]
    async fn test_encryption_context_creation() {
        let policy = crate::security::SecurityPolicy::default();
        let encryption_manager = ChannelEncryptionManager::new(&policy).await.unwrap();
        
        // 模拟连接
        // 这里需要实际的IpcConnection实现来测试
    }
    
    #[tokio::test]
    async fn test_session_management() {
        let policy = crate::security::SecurityPolicy::default();
        let session_manager = SecureSessionManager::new(&policy).await.unwrap();
        
        let session_info = SessionInfo {
            session_id: "test-session".to_string(),
            connection_id: "test-connection".to_string(),
            encryption_enabled: true,
            authentication_method: AuthenticationMethod::ProcessSignature,
            created_at: Utc::now(),
        };
        
        session_manager.register_session(&session_info, None).await.unwrap();
        
        let is_valid = session_manager.validate_session("test-session").await.unwrap();
        assert!(is_valid);
        
        let count = session_manager.active_session_count().await;
        assert_eq!(count, 1);
    }

    /// 测试IPC安全管理器的完整生命周期
    #[tokio::test]
    async fn test_ipc_security_manager_lifecycle() {
        let policy = crate::security::SecurityPolicy::default();
        let manager = IpcSecurityManager::new(&policy).await.unwrap();

        // 测试初始化
        let result = manager.initialize().await;
        assert!(result.is_ok(), "IPC安全管理器初始化失败");

        // 验证状态
        let status = manager.get_status().await.unwrap();
        assert!(status.initialized, "IPC安全管理器未正确初始化");

        // 测试关闭
        let result = manager.shutdown().await;
        assert!(result.is_ok(), "IPC安全管理器关闭失败");
    }

    /// 测试加密解密功能的正确性
    #[tokio::test]
    async fn test_encryption_decryption_correctness() {
        let policy = crate::security::SecurityPolicy::default();
        let encryption_manager = ChannelEncryptionManager::new(&policy).await.unwrap();

        // 创建测试会话
        let session_id = "test_encryption_session";
        let test_data = b"Hello, this is a test message for encryption!";

        // 模拟创建加密上下文
        let encryption_context = EncryptionContext {
            session_id: session_id.to_string(),
            cipher_suite: CipherSuite::ChaCha20Poly1305,
            encryption_key: vec![0x42; 32], // 测试密钥
            hmac_key: vec![0x24; 32], // 测试HMAC密钥
            created_at: chrono::Utc::now(),
            last_used: chrono::Utc::now(),
        };

        // 手动添加加密上下文
        encryption_manager.encryption_contexts.write().await
            .insert(session_id.to_string(), encryption_context);

        // 测试加密
        let encrypted_result = encryption_manager.encrypt_data(session_id, test_data).await;
        assert!(encrypted_result.is_ok(), "加密失败");
        let encrypted_data = encrypted_result.unwrap();
        assert_ne!(encrypted_data, test_data, "加密后数据应该不同");
        assert!(encrypted_data.len() > test_data.len(), "加密后数据应该更长（包含认证标签）");

        // 测试解密
        let decrypted_result = encryption_manager.decrypt_data(session_id, &encrypted_data).await;
        assert!(decrypted_result.is_ok(), "解密失败");
        let decrypted_data = decrypted_result.unwrap();
        assert_eq!(decrypted_data, test_data, "解密后数据应该与原始数据相同");
    }

    /// 测试加密/解密的边界情况
    #[tokio::test]
    async fn test_encryption_edge_cases() {
        let policy = crate::security::SecurityPolicy::default();
        let encryption_manager = ChannelEncryptionManager::new(&policy).await.unwrap();
        let session_id = "test-edge-cases";

        // 创建加密上下文
        let encryption_context = EncryptionContext {
            session_id: session_id.to_string(),
            encryption_key: vec![0x42; 32], // 32字节密钥
            hmac_key: vec![0x24; 16],
            cipher_suite: CipherSuite::ChaCha20Poly1305,
            created_at: chrono::Utc::now(),
            last_used: chrono::Utc::now(),
        };

        encryption_manager.encryption_contexts.write().await
            .insert(session_id.to_string(), encryption_context);

        // 测试空数据加密/解密
        let empty_data = b"";
        let encrypted_empty = encryption_manager.encrypt_data(session_id, empty_data).await.unwrap();
        println!("空数据加密后长度: {} 字节", encrypted_empty.len());
        assert!(encrypted_empty.len() >= 28, "加密后的空数据应该至少有28字节（nonce(12) + auth_tag(16)）");
        let decrypted_empty = encryption_manager.decrypt_data(session_id, &encrypted_empty).await.unwrap();
        assert_eq!(decrypted_empty, empty_data);

        // 测试单字节数据
        let single_byte = b"A";
        let encrypted_single = encryption_manager.encrypt_data(session_id, single_byte).await.unwrap();
        let decrypted_single = encryption_manager.decrypt_data(session_id, &encrypted_single).await.unwrap();
        assert_eq!(decrypted_single, single_byte);

        // 测试大数据块
        let large_data = vec![0x55; 10000]; // 10KB数据
        let encrypted_large = encryption_manager.encrypt_data(session_id, &large_data).await.unwrap();
        let decrypted_large = encryption_manager.decrypt_data(session_id, &encrypted_large).await.unwrap();
        assert_eq!(decrypted_large, large_data);

        println!("✅ 加密/解密边界情况测试通过");
    }

    /// 测试会话管理的完整功能
    #[tokio::test]
    async fn test_complete_session_management() {
        let policy = crate::security::SecurityPolicy::default();
        let session_manager = SecureSessionManager::new(&policy).await.unwrap();
        session_manager.initialize().await.unwrap();

        // 创建测试会话
        let session_info = SessionInfo {
            session_id: "complete-test-session".to_string(),
            connection_id: "complete-test-connection".to_string(),
            encryption_enabled: true,
            authentication_method: AuthenticationMethod::ProcessSignature,
            created_at: Utc::now(),
        };

        // 注册会话
        let result = session_manager.register_session(&session_info, None).await;
        assert!(result.is_ok(), "会话注册失败");

        // 验证会话
        let validation_result = session_manager.validate_session(&session_info.session_id).await;
        assert!(validation_result.is_ok(), "会话验证失败");
        assert!(validation_result.unwrap(), "会话应该有效");

        // 更新会话活动时间
        let update_result = session_manager.update_session_activity(&session_info.session_id).await;
        assert!(update_result.is_ok(), "会话活动时间更新失败");

        // 检查活动会话数量
        let count = session_manager.active_session_count().await;
        assert_eq!(count, 1, "活动会话数量应为1");

        // 终止会话
        let terminate_result = session_manager.terminate_session(&session_info.session_id).await;
        assert!(terminate_result.is_ok(), "会话终止失败");

        // 验证会话已被删除
        let validation_after_terminate = session_manager.validate_session(&session_info.session_id).await;
        assert!(validation_after_terminate.is_ok(), "验证操作应该成功");
        assert!(!validation_after_terminate.unwrap(), "已终止的会话应该无效");

        session_manager.shutdown().await.unwrap();
    }

    /// 测试并发会话操作
    #[tokio::test]
    async fn test_concurrent_session_operations() {
        let policy = crate::security::SecurityPolicy::default();
        let session_manager = std::sync::Arc::new(SecureSessionManager::new(&policy).await.unwrap());
        session_manager.initialize().await.unwrap();

        let mut handles = Vec::new();
        let session_count = 20;

        // 并发创建多个会话
        for i in 0..session_count {
            let session_manager_clone = std::sync::Arc::clone(&session_manager);
            let handle = tokio::spawn(async move {
                let session_info = SessionInfo {
                    session_id: format!("concurrent-session-{}", i),
                    connection_id: format!("concurrent-connection-{}", i),
                    encryption_enabled: i % 2 == 0, // 交替启用加密
                    authentication_method: AuthenticationMethod::ProcessSignature,
                    created_at: Utc::now(),
                };

                session_manager_clone.register_session(&session_info, None).await
            });
            handles.push(handle);
        }

        // 等待所有会话创建完成
        let mut successful_sessions = 0;
        for handle in handles {
            let result = handle.await.unwrap();
            if result.is_ok() {
                successful_sessions += 1;
            }
        }

        assert_eq!(successful_sessions, session_count, "所有会话都应该创建成功");

        // 验证最终会话数量
        let final_count = session_manager.active_session_count().await;
        assert_eq!(final_count, session_count, "最终会话数量应该正确");

        session_manager.shutdown().await.unwrap();
    }

    /// 测试错误处理和边界条件
    #[tokio::test]
    async fn test_error_handling_and_boundary_conditions() {
        let policy = crate::security::SecurityPolicy::default();
        let session_manager = SecureSessionManager::new(&policy).await.unwrap();
        let encryption_manager = ChannelEncryptionManager::new(&policy).await.unwrap();

        // 测试无效会话ID
        let invalid_session_result = session_manager.validate_session("invalid-session-id").await;
        assert!(invalid_session_result.is_ok(), "验证操作应该成功");
        assert!(!invalid_session_result.unwrap(), "无效会话ID应该返回false");

        // 测试空数据加密
        let empty_data_result = encryption_manager.encrypt_data("nonexistent-session", b"").await;
        assert!(empty_data_result.is_err(), "不存在的会话应该加密失败");

        // 测试无效加密数据解密
        let invalid_decrypt_result = encryption_manager.decrypt_data("nonexistent-session", b"invalid-data").await;
        assert!(invalid_decrypt_result.is_err(), "无效数据应该解密失败");

        // 测试重复终止会话
        let terminate_result1 = session_manager.terminate_session("nonexistent-session").await;
        let terminate_result2 = session_manager.terminate_session("nonexistent-session").await;
        // 终止不存在的会话应该优雅处理
        assert!(terminate_result1.is_ok(), "终止不存在的会话应该优雅处理");
        assert!(terminate_result2.is_ok(), "重复终止应该优雅处理");
    }

    /// 测试加密密钥生成和管理
    #[tokio::test]
    async fn test_encryption_key_generation() {
        let policy = crate::security::SecurityPolicy::default();
        let encryption_manager = ChannelEncryptionManager::new(&policy).await.unwrap();

        // 测试会话密钥生成
        let key_result = encryption_manager.generate_session_key().await;
        assert!(key_result.is_ok(), "会话密钥生成失败");
        let key = key_result.unwrap();
        assert_eq!(key.len(), 32, "会话密钥长度应为32字节");

        // 测试随机数生成
        let nonce_result = encryption_manager.generate_nonce().await;
        assert!(nonce_result.is_ok(), "随机数生成失败");
        let nonce = nonce_result.unwrap();
        assert_eq!(nonce.len(), 12, "随机数长度应为12字节");

        // 测试多次生成的密钥应该不同
        let key2 = encryption_manager.generate_session_key().await.unwrap();
        assert_ne!(key, key2, "多次生成的密钥应该不同");
    }

    /// 测试会话超时处理
    #[tokio::test]
    async fn test_session_timeout_handling() {
        let mut policy = crate::security::SecurityPolicy::default();
        policy.session_timeout_seconds = 1; // 1秒超时用于测试

        let session_manager = SecureSessionManager::new(&policy).await.unwrap();
        session_manager.initialize().await.unwrap();

        // 创建会话
        let session_info = SessionInfo {
            session_id: "timeout-test-session".to_string(),
            connection_id: "timeout-test-connection".to_string(),
            encryption_enabled: false,
            authentication_method: AuthenticationMethod::ProcessSignature,
            created_at: Utc::now(),
        };

        session_manager.register_session(&session_info, None).await.unwrap();

        // 验证会话有效
        let valid_result = session_manager.validate_session(&session_info.session_id).await.unwrap();
        assert!(valid_result, "新创建的会话应该有效");

        // 等待超时
        tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

        // 清理过期会话
        session_manager.cleanup_expired_sessions().await.unwrap();

        // 验证会话已过期
        let expired_result = session_manager.validate_session(&session_info.session_id).await.unwrap();
        assert!(!expired_result, "过期的会话应该无效");

        session_manager.shutdown().await.unwrap();
    }
}